2025-08-02 00:02:39 | DEBUG | 收到消息: {'MsgId': 334733037, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_sf4ikl8sczaj21:\n@壹壹.\u2005宝儿，我有'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754064166, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_if9bozh3yp522</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_CzNQN8ed|v1_YVnwbUx0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8633031992292007647, 'MsgSeq': 871419411}
2025-08-02 00:02:39 | INFO | 收到文本消息: 消息ID:334733037 来自:***********@chatroom 发送人:wxid_sf4ikl8sczaj21 @:['wxid_if9bozh3yp522'] 内容:@壹壹. 宝儿，我有
2025-08-02 00:02:39 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@壹壹. 宝儿，我有' from wxid_sf4ikl8sczaj21 in ***********@chatroom
2025-08-02 00:02:39 | DEBUG | [DouBaoImageToImage] 命令解析: ['@壹壹.\u2005宝儿，我有']
2025-08-02 00:02:39 | DEBUG | 处理消息内容: '@壹壹. 宝儿，我有'
2025-08-02 00:02:39 | DEBUG | 消息内容 '@壹壹. 宝儿，我有' 不匹配任何命令，忽略
2025-08-02 00:02:56 | DEBUG | 收到消息: {'MsgId': 941052946, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_if9bozh3yp522:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>给我一张 宝宝</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>8633031992292007647</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_sf4ikl8sczaj21</chatusr>\n\t\t\t<displayname>Untitled</displayname>\n\t\t\t<content>@壹壹.\u2005宝儿，我有</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;839359491&lt;/sequence_id&gt;\n\t&lt;atuserlist&gt;wxid_if9bozh3yp522&lt;/atuserlist&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;148&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_AStWmI0l|v1_62w3YCGK&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754064166</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_if9bozh3yp522</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754064183, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>3d8657a91d71ae62dec787c0bc74fdc1_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_R9qEtVEy|v1_aaJ4MZxU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4278920047956410221, 'MsgSeq': 871419412}
2025-08-02 00:02:56 | DEBUG | 从群聊消息中提取发送者: wxid_if9bozh3yp522
2025-08-02 00:02:56 | DEBUG | 使用已解析的XML处理引用消息
2025-08-02 00:02:56 | INFO | 收到引用消息: 消息ID:941052946 来自:***********@chatroom 发送人:wxid_if9bozh3yp522 内容:给我一张 宝宝 引用类型:1
2025-08-02 00:02:56 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-02 00:02:56 | INFO | [DouBaoImageToImage] 消息内容: '给我一张 宝宝' from wxid_if9bozh3yp522 in ***********@chatroom
2025-08-02 00:02:56 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['给我一张', '宝宝']
2025-08-02 00:02:56 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-02 00:02:56 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-02 00:02:56 | INFO |   - 消息内容: 给我一张 宝宝
2025-08-02 00:02:56 | INFO |   - 群组ID: ***********@chatroom
2025-08-02 00:02:56 | INFO |   - 发送人: wxid_if9bozh3yp522
2025-08-02 00:02:56 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '@壹壹.\u2005宝儿，我有', 'Msgid': '8633031992292007647', 'NewMsgId': '8633031992292007647', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': 'Untitled', 'MsgSource': '<msgsource><sequence_id>839359491</sequence_id>\n\t<atuserlist>wxid_if9bozh3yp522</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_AStWmI0l|v1_62w3YCGK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754064166', 'SenderWxid': 'wxid_if9bozh3yp522'}
2025-08-02 00:02:56 | INFO |   - 引用消息ID: 
2025-08-02 00:02:56 | INFO |   - 引用消息类型: 
2025-08-02 00:02:56 | INFO |   - 引用消息内容: @壹壹. 宝儿，我有
2025-08-02 00:02:56 | INFO |   - 引用消息发送人: wxid_if9bozh3yp522
2025-08-02 00:03:13 | DEBUG | 收到消息: {'MsgId': 158208109, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_sf4ikl8sczaj21:\n这个号吗？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754064201, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_4QiVE7X5|v1_s9cWl5Tk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7954478224058467809, 'MsgSeq': 871419413}
2025-08-02 00:03:13 | INFO | 收到文本消息: 消息ID:158208109 来自:***********@chatroom 发送人:wxid_sf4ikl8sczaj21 @:[] 内容:这个号吗？
2025-08-02 00:03:13 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这个号吗？' from wxid_sf4ikl8sczaj21 in ***********@chatroom
2025-08-02 00:03:13 | DEBUG | [DouBaoImageToImage] 命令解析: ['这个号吗？']
2025-08-02 00:03:13 | DEBUG | 处理消息内容: '这个号吗？'
2025-08-02 00:03:13 | DEBUG | 消息内容 '这个号吗？' 不匹配任何命令，忽略
2025-08-02 00:03:33 | DEBUG | 收到消息: {'MsgId': 53765767, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_if9bozh3yp522:\n嗯嗯'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754064221, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_u+/0USfE|v1_ogeSTF41</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8913422600766735977, 'MsgSeq': 871419414}
2025-08-02 00:03:33 | INFO | 收到文本消息: 消息ID:53765767 来自:***********@chatroom 发送人:wxid_if9bozh3yp522 @:[] 内容:嗯嗯
2025-08-02 00:03:33 | DEBUG | [DouBaoImageToImage] 收到文本消息: '嗯嗯' from wxid_if9bozh3yp522 in ***********@chatroom
2025-08-02 00:03:33 | DEBUG | [DouBaoImageToImage] 命令解析: ['嗯嗯']
2025-08-02 00:03:33 | DEBUG | 处理消息内容: '嗯嗯'
2025-08-02 00:03:33 | DEBUG | 消息内容 '嗯嗯' 不匹配任何命令，忽略
2025-08-02 00:03:57 | DEBUG | 收到消息: {'MsgId': 2097101230, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_if9bozh3yp522:\n<msg><emoji fromusername="wxid_if9bozh3yp522" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="50cc35c2fa9df4f729d879c51df0468b" len="10727" productid="" androidmd5="50cc35c2fa9df4f729d879c51df0468b" androidlen="10727" s60v3md5="50cc35c2fa9df4f729d879c51df0468b" s60v3len="10727" s60v5md5="50cc35c2fa9df4f729d879c51df0468b" s60v5len="10727" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=50cc35c2fa9df4f729d879c51df0468b&amp;filekey=3043020101042f302d02016e0402535a04203530636333356332666139646634663732396438373963353164663034363862020229e7040d00000004627466730000000132&amp;hy=SZ&amp;storeid=265d45a28000e5951104a6afd0000006e01004fb1535a04265bc1e6744a228&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=0d8364628706520ab0707af5cbc127c3&amp;filekey=3043020101042f302d02016e0402535a04203064383336343632383730363532306162303730376166356362633132376333020229f0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=265d45a28000ef2ce104a6afd0000006e02004fb2535a04265bc1e6744a233&amp;ef=2&amp;bizid=1022" aeskey="041b386bb669491699e0fed8517f8871" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=91ceb0fee453b5d9a685c022c6b9688b&amp;filekey=3043020101042f302d02016e0402535a0420393163656230666565343533623564396136383563303232633662393638386202021370040d00000004627466730000000132&amp;hy=SZ&amp;storeid=265d45a2900002757104a6afd0000006e03004fb3535a04265bc1e6744a23a&amp;ef=3&amp;bizid=1022" externmd5="cce96d58948d400137f8ae487df7d6a9" width="234" height="240" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754064245, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_Q0Wo0JYK|v1_VaL5N8i8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7463090830661146910, 'MsgSeq': 871419415}
2025-08-02 00:03:57 | INFO | 收到表情消息: 消息ID:2097101230 来自:***********@chatroom 发送人:wxid_if9bozh3yp522 MD5:50cc35c2fa9df4f729d879c51df0468b 大小:10727
2025-08-02 00:03:57 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7463090830661146910
2025-08-02 00:04:17 | DEBUG | 收到消息: {'MsgId': 1632470908, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_sf4ikl8sczaj21:\n<msg><emoji fromusername = "wxid_sf4ikl8sczaj21" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="c289a75bac39e1c390fdb560d5b1640d" len = "718078" productid="" androidmd5="c289a75bac39e1c390fdb560d5b1640d" androidlen="718078" s60v3md5 = "c289a75bac39e1c390fdb560d5b1640d" s60v3len="718078" s60v5md5 = "c289a75bac39e1c390fdb560d5b1640d" s60v5len="718078" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=c289a75bac39e1c390fdb560d5b1640d&amp;filekey=30350201010421301f02020106040253480410c289a75bac39e1c390fdb560d5b1640d02030af4fe040d00000004627466730000000132&amp;hy=SH&amp;storeid=2631e15410005ede6000000000000010600004f5053480a1c5960973b34adf&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=48f0264b097b8e9d611f797349846e0a&amp;filekey=30350201010421301f0202010604025348041048f0264b097b8e9d611f797349846e0a02030af500040d00000004627466730000000132&amp;hy=SH&amp;storeid=2631e1541000b9e0e000000000000010600004f50534808467b40b7455350b&amp;bizid=1023" aeskey= "6b52bb0575d20cb543285a36b1e48a15" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=861f1bb12af15e81bb9680d5014e20a3&amp;filekey=30350201010421301f02020106040253480410861f1bb12af15e81bb9680d5014e20a3020300f420040d00000004627466730000000132&amp;hy=SH&amp;storeid=2631e1541000ebee0000000000000010600004f50534814dc5960973b90f3d&amp;bizid=1023" externmd5 = "bcafa39fa00dc3b745918a71b5435455" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754064265, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_oD/1as5x|v1_/VTvhxKk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7076349823902075982, 'MsgSeq': 871419416}
2025-08-02 00:04:17 | INFO | 收到表情消息: 消息ID:1632470908 来自:***********@chatroom 发送人:wxid_sf4ikl8sczaj21 MD5:c289a75bac39e1c390fdb560d5b1640d 大小:718078
2025-08-02 00:04:17 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7076349823902075982
2025-08-02 00:10:14 | DEBUG | 收到消息: {'MsgId': 1276582243, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_62fiham2pn7521:\n使用 `Step3` 模型联网\n\nGiffgaff 是英国移动虚拟网络运营商（隶属于 O2 旗下），**“好用与否”完全取决于你的使用场景**，以下从「优势」「短板」「适用人群」三方面拆解：  \n\n\n### 一、值得选的 3 个核心优势  \n1. **“保号”成本极低，适合长期持有海外号**  \n   Giffgaff 只需**每 180 天有 1 次余额变动**（发 1 条短信/充 1 英镑），就能保留号码。发短信成本仅 0.3 英镑（≈2.7 元），一年保号费约 5.4 元，是海外“保号卡”里成本最低的一档 <web_4f505062>。  \n\n2. **免费收短信 + 无月租，注册海外平台“神器”**  \n   收全球短信免费、无月租，能完美注册 **ChatGPT、Claude、Stripe、TikTok** 等海外平台（部分平台对 +86 号码限制多，用 Giffgaff 更“安全”）。淘宝上 120 元左右就能买到实体卡，激活后即可用 <web_4f505062>。  \n\n3. **英国本土福利：用户间免费通话**  \n   在英国境内，Giffgaff 用户之间打电话、发短信全免费，适合留学生/短期旅英人群 <web_d9bf925b>。  \n\n\n### 二、必须避开的 3 个致命短板  \n1. **国内漫游资费“天价”，用流量/通话=烧钱**  \n   - 流量：0.2 英镑/MB（≈1.8 元/MB），1GB 流量要花 200 英镑（≈1800 元）<web_4f505062>；  \n   - 通话/短信：接打电话 1 英镑/分钟（≈9 元/分钟），发短信 0.3 英镑/条（≈2.7 元/条）<web_4f505062>；  \n   → 在中国**千万别用它上网/打电话**，只用来收短信！  \n\n2. **信号依赖英国 O2 网络，国内几乎无信号**  \n   作为虚拟运营商，Giffgaff 借用 O2 网络，在英国本土部分区域（如偏远乡村）信号都弱，在中国更别指望能用蜂窝网络 <web_d9bf925b>。  \n\n3. **激活/充值略麻烦，依赖境外支付**  \n   激活需官网输卡上编号，充值要 VISA/MasterCard（不支持银联/支付宝），部分用户反馈有隐藏手续费 <web_4f505062>。  \n\n\n### 三、这张卡适合谁？  \n✅ 需要**注册海外平台**（AI 工具、跨境电商、国际社交账号）的用户；  \n✅ 想**低成本保留“非 +86 号码”**（当“外网通行证”）的轻量需求者；  \n❌ 在中国需要**流量/通话当主力卡**的用户（漫游费会让你破产）。  \n\n\n简单总结：Giffgaff 是**“功能极其专一”的工具卡**——只擅长“保号 + 收海外短信”，其他场景全是短板。如果你的需求匹配，它就是“神卡”；反之则毫无价值～ '}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754064621, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>5</cf>\n\t\t<inlenlist>1208</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>v1_9FIdDYP8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。 : 使用 `Step3` 模型联网\n\nGiffgaff 是英国移动虚拟网络运营商（隶属于 O2 ...', 'NewMsgId': 990903629251725829, 'MsgSeq': 871419417}
2025-08-02 00:10:14 | INFO | 收到文本消息: 消息ID:1276582243 来自:47325400669@chatroom 发送人:wxid_62fiham2pn7521 @:[] 内容:使用 `Step3` 模型联网

Giffgaff 是英国移动虚拟网络运营商（隶属于 O2 旗下），**“好用与否”完全取决于你的使用场景**，以下从「优势」「短板」「适用人群」三方面拆解：  


### 一、值得选的 3 个核心优势  
1. **“保号”成本极低，适合长期持有海外号**  
   Giffgaff 只需**每 180 天有 1 次余额变动**（发 1 条短信/充 1 英镑），就能保留号码。发短信成本仅 0.3 英镑（≈2.7 元），一年保号费约 5.4 元，是海外“保号卡”里成本最低的一档 <web_4f505062>。  

2. **免费收短信 + 无月租，注册海外平台“神器”**  
   收全球短信免费、无月租，能完美注册 **ChatGPT、Claude、Stripe、TikTok** 等海外平台（部分平台对 +86 号码限制多，用 Giffgaff 更“安全”）。淘宝上 120 元左右就能买到实体卡，激活后即可用 <web_4f505062>。  

3. **英国本土福利：用户间免费通话**  
   在英国境内，Giffgaff 用户之间打电话、发短信全免费，适合留学生/短期旅英人群 <web_d9bf925b>。  


### 二、必须避开的 3 个致命短板  
1. **国内漫游资费“天价”，用流量/通话=烧钱**  
   - 流量：0.2 英镑/MB（≈1.8 元/MB），1GB 流量要花 200 英镑（≈1800 元）<web_4f505062>；  
   - 通话/短信：接打电话 1 英镑/分钟（≈9 元/分钟），发短信 0.3 英镑/条（≈2.7 元/条）<web_4f505062>；  
   → 在中国**千万别用它上网/打电话**，只用来收短信！  

2. **信号依赖英国 O2 网络，国内几乎无信号**  
   作为虚拟运营商，Giffgaff 借用 O2 网络，在英国本土部分区域（如偏远乡村）信号都弱，在中国更别指望能用蜂窝网络 <web_d9bf925b>。  

3. **激活/充值略麻烦，依赖境外支付**  
   激活需官网输卡上编号，充值要 VISA/MasterCard（不支持银联/支付宝），部分用户反馈有隐藏手续费 <web_4f505062>。  


### 三、这张卡适合谁？  
✅ 需要**注册海外平台**（AI 工具、跨境电商、国际社交账号）的用户；  
✅ 想**低成本保留“非 +86 号码”**（当“外网通行证”）的轻量需求者；  
❌ 在中国需要**流量/通话当主力卡**的用户（漫游费会让你破产）。  


简单总结：Giffgaff 是**“功能极其专一”的工具卡**——只擅长“保号 + 收海外短信”，其他场景全是短板。如果你的需求匹配，它就是“神卡”；反之则毫无价值～ 
2025-08-02 00:10:14 | DEBUG | [DouBaoImageToImage] 收到文本消息: '使用 `Step3` 模型联网

Giffgaff 是英国移动虚拟网络运营商（隶属于 O2 旗下），**“好用与否”完全取决于你的使用场景**，以下从「优势」「短板」「适用人群」三方面拆解：  


### 一、值得选的 3 个核心优势  
1. **“保号”成本极低，适合长期持有海外号**  
   Giffgaff 只需**每 180 天有 1 次余额变动**（发 1 条短信/充 1 英镑），就能保留号码。发短信成本仅 0.3 英镑（≈2.7 元），一年保号费约 5.4 元，是海外“保号卡”里成本最低的一档 <web_4f505062>。  

2. **免费收短信 + 无月租，注册海外平台“神器”**  
   收全球短信免费、无月租，能完美注册 **ChatGPT、Claude、Stripe、TikTok** 等海外平台（部分平台对 +86 号码限制多，用 Giffgaff 更“安全”）。淘宝上 120 元左右就能买到实体卡，激活后即可用 <web_4f505062>。  

3. **英国本土福利：用户间免费通话**  
   在英国境内，Giffgaff 用户之间打电话、发短信全免费，适合留学生/短期旅英人群 <web_d9bf925b>。  


### 二、必须避开的 3 个致命短板  
1. **国内漫游资费“天价”，用流量/通话=烧钱**  
   - 流量：0.2 英镑/MB（≈1.8 元/MB），1GB 流量要花 200 英镑（≈1800 元）<web_4f505062>；  
   - 通话/短信：接打电话 1 英镑/分钟（≈9 元/分钟），发短信 0.3 英镑/条（≈2.7 元/条）<web_4f505062>；  
   → 在中国**千万别用它上网/打电话**，只用来收短信！  

2. **信号依赖英国 O2 网络，国内几乎无信号**  
   作为虚拟运营商，Giffgaff 借用 O2 网络，在英国本土部分区域（如偏远乡村）信号都弱，在中国更别指望能用蜂窝网络 <web_d9bf925b>。  

3. **激活/充值略麻烦，依赖境外支付**  
   激活需官网输卡上编号，充值要 VISA/MasterCard（不支持银联/支付宝），部分用户反馈有隐藏手续费 <web_4f505062>。  


### 三、这张卡适合谁？  
✅ 需要**注册海外平台**（AI 工具、跨境电商、国际社交账号）的用户；  
✅ 想**低成本保留“非 +86 号码”**（当“外网通行证”）的轻量需求者；  
❌ 在中国需要**流量/通话当主力卡**的用户（漫游费会让你破产）。  


简单总结：Giffgaff 是**“功能极其专一”的工具卡**——只擅长“保号 + 收海外短信”，其他场景全是短板。如果你的需求匹配，它就是“神卡”；反之则毫无价值～' from wxid_62fiham2pn7521 in 47325400669@chatroom
2025-08-02 00:10:14 | DEBUG | [DouBaoImageToImage] 命令解析: ['使用', '`Step3`', '模型联网\n\nGiffgaff 是英国移动虚拟网络运营商（隶属于 O2 旗下），**“好用与否”完全取决于你的使用场景**，以下从「优势」「短板」「适用人群」三方面拆解：  \n\n\n### 一、值得选的 3 个核心优势  \n1. **“保号”成本极低，适合长期持有海外号**  \n   Giffgaff 只需**每 180 天有 1 次余额变动**（发 1 条短信/充 1 英镑），就能保留号码。发短信成本仅 0.3 英镑（≈2.7 元），一年保号费约 5.4 元，是海外“保号卡”里成本最低的一档 <web_4f505062>。  \n\n2. **免费收短信 + 无月租，注册海外平台“神器”**  \n   收全球短信免费、无月租，能完美注册 **ChatGPT、Claude、Stripe、TikTok** 等海外平台（部分平台对 +86 号码限制多，用 Giffgaff 更“安全”）。淘宝上 120 元左右就能买到实体卡，激活后即可用 <web_4f505062>。  \n\n3. **英国本土福利：用户间免费通话**  \n   在英国境内，Giffgaff 用户之间打电话、发短信全免费，适合留学生/短期旅英人群 <web_d9bf925b>。  \n\n\n### 二、必须避开的 3 个致命短板  \n1. **国内漫游资费“天价”，用流量/通话=烧钱**  \n   - 流量：0.2 英镑/MB（≈1.8 元/MB），1GB 流量要花 200 英镑（≈1800 元）<web_4f505062>；  \n   - 通话/短信：接打电话 1 英镑/分钟（≈9 元/分钟），发短信 0.3 英镑/条（≈2.7 元/条）<web_4f505062>；  \n   → 在中国**千万别用它上网/打电话**，只用来收短信！  \n\n2. **信号依赖英国 O2 网络，国内几乎无信号**  \n   作为虚拟运营商，Giffgaff 借用 O2 网络，在英国本土部分区域（如偏远乡村）信号都弱，在中国更别指望能用蜂窝网络 <web_d9bf925b>。  \n\n3. **激活/充值略麻烦，依赖境外支付**  \n   激活需官网输卡上编号，充值要 VISA/MasterCard（不支持银联/支付宝），部分用户反馈有隐藏手续费 <web_4f505062>。  \n\n\n### 三、这张卡适合谁？  \n✅ 需要**注册海外平台**（AI 工具、跨境电商、国际社交账号）的用户；  \n✅ 想**低成本保留“非 +86 号码”**（当“外网通行证”）的轻量需求者；  \n❌ 在中国需要**流量/通话当主力卡**的用户（漫游费会让你破产）。  \n\n\n简单总结：Giffgaff 是**“功能极其专一”的工具卡**——只擅长“保号 + 收海外短信”，其他场景全是短板。如果你的需求匹配，它就是“神卡”；反之则毫无价值～']
2025-08-02 00:10:14 | DEBUG | 处理消息内容: '使用 `Step3` 模型联网

Giffgaff 是英国移动虚拟网络运营商（隶属于 O2 旗下），**“好用与否”完全取决于你的使用场景**，以下从「优势」「短板」「适用人群」三方面拆解：  


### 一、值得选的 3 个核心优势  
1. **“保号”成本极低，适合长期持有海外号**  
   Giffgaff 只需**每 180 天有 1 次余额变动**（发 1 条短信/充 1 英镑），就能保留号码。发短信成本仅 0.3 英镑（≈2.7 元），一年保号费约 5.4 元，是海外“保号卡”里成本最低的一档 <web_4f505062>。  

2. **免费收短信 + 无月租，注册海外平台“神器”**  
   收全球短信免费、无月租，能完美注册 **ChatGPT、Claude、Stripe、TikTok** 等海外平台（部分平台对 +86 号码限制多，用 Giffgaff 更“安全”）。淘宝上 120 元左右就能买到实体卡，激活后即可用 <web_4f505062>。  

3. **英国本土福利：用户间免费通话**  
   在英国境内，Giffgaff 用户之间打电话、发短信全免费，适合留学生/短期旅英人群 <web_d9bf925b>。  


### 二、必须避开的 3 个致命短板  
1. **国内漫游资费“天价”，用流量/通话=烧钱**  
   - 流量：0.2 英镑/MB（≈1.8 元/MB），1GB 流量要花 200 英镑（≈1800 元）<web_4f505062>；  
   - 通话/短信：接打电话 1 英镑/分钟（≈9 元/分钟），发短信 0.3 英镑/条（≈2.7 元/条）<web_4f505062>；  
   → 在中国**千万别用它上网/打电话**，只用来收短信！  

2. **信号依赖英国 O2 网络，国内几乎无信号**  
   作为虚拟运营商，Giffgaff 借用 O2 网络，在英国本土部分区域（如偏远乡村）信号都弱，在中国更别指望能用蜂窝网络 <web_d9bf925b>。  

3. **激活/充值略麻烦，依赖境外支付**  
   激活需官网输卡上编号，充值要 VISA/MasterCard（不支持银联/支付宝），部分用户反馈有隐藏手续费 <web_4f505062>。  


### 三、这张卡适合谁？  
✅ 需要**注册海外平台**（AI 工具、跨境电商、国际社交账号）的用户；  
✅ 想**低成本保留“非 +86 号码”**（当“外网通行证”）的轻量需求者；  
❌ 在中国需要**流量/通话当主力卡**的用户（漫游费会让你破产）。  


简单总结：Giffgaff 是**“功能极其专一”的工具卡**——只擅长“保号 + 收海外短信”，其他场景全是短板。如果你的需求匹配，它就是“神卡”；反之则毫无价值～'
2025-08-02 00:10:14 | DEBUG | 消息内容 '使用 `Step3` 模型联网

Giffgaff 是英国移动虚拟网络运营商（隶属于 O2 旗下），**“好用与否”完全取决于你的使用场景**，以下从「优势」「短板」「适用人群」三方面拆解：  


### 一、值得选的 3 个核心优势  
1. **“保号”成本极低，适合长期持有海外号**  
   Giffgaff 只需**每 180 天有 1 次余额变动**（发 1 条短信/充 1 英镑），就能保留号码。发短信成本仅 0.3 英镑（≈2.7 元），一年保号费约 5.4 元，是海外“保号卡”里成本最低的一档 <web_4f505062>。  

2. **免费收短信 + 无月租，注册海外平台“神器”**  
   收全球短信免费、无月租，能完美注册 **ChatGPT、Claude、Stripe、TikTok** 等海外平台（部分平台对 +86 号码限制多，用 Giffgaff 更“安全”）。淘宝上 120 元左右就能买到实体卡，激活后即可用 <web_4f505062>。  

3. **英国本土福利：用户间免费通话**  
   在英国境内，Giffgaff 用户之间打电话、发短信全免费，适合留学生/短期旅英人群 <web_d9bf925b>。  


### 二、必须避开的 3 个致命短板  
1. **国内漫游资费“天价”，用流量/通话=烧钱**  
   - 流量：0.2 英镑/MB（≈1.8 元/MB），1GB 流量要花 200 英镑（≈1800 元）<web_4f505062>；  
   - 通话/短信：接打电话 1 英镑/分钟（≈9 元/分钟），发短信 0.3 英镑/条（≈2.7 元/条）<web_4f505062>；  
   → 在中国**千万别用它上网/打电话**，只用来收短信！  

2. **信号依赖英国 O2 网络，国内几乎无信号**  
   作为虚拟运营商，Giffgaff 借用 O2 网络，在英国本土部分区域（如偏远乡村）信号都弱，在中国更别指望能用蜂窝网络 <web_d9bf925b>。  

3. **激活/充值略麻烦，依赖境外支付**  
   激活需官网输卡上编号，充值要 VISA/MasterCard（不支持银联/支付宝），部分用户反馈有隐藏手续费 <web_4f505062>。  


### 三、这张卡适合谁？  
✅ 需要**注册海外平台**（AI 工具、跨境电商、国际社交账号）的用户；  
✅ 想**低成本保留“非 +86 号码”**（当“外网通行证”）的轻量需求者；  
❌ 在中国需要**流量/通话当主力卡**的用户（漫游费会让你破产）。  


简单总结：Giffgaff 是**“功能极其专一”的工具卡**——只擅长“保号 + 收海外短信”，其他场景全是短板。如果你的需求匹配，它就是“神卡”；反之则毫无价值～' 不匹配任何命令，忽略
2025-08-02 00:23:43 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-02 00:23:43 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-08-02 00:23:44 | DEBUG | 群成员变化检查完成
2025-08-02 00:53:43 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-02 00:57:51 | DEBUG | 收到消息: {'MsgId': 789761076, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n@壹壹.\u2005 你明天问@饿飞他 我也不知道他这个打不打\u2005'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754067478, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_if9bozh3yp522</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_EsyWVgQx|v1_CGMCGDE0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3715052507572711146, 'MsgSeq': 871419418}
2025-08-02 00:57:51 | INFO | 收到文本消息: 消息ID:789761076 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:['wxid_if9bozh3yp522'] 内容:@壹壹.  你明天问@饿飞他 我也不知道他这个打不打 
2025-08-02 00:57:51 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@壹壹.  你明天问@饿飞他 我也不知道他这个打不打' from wxid_1ul5r40nibpn12 in ***********@chatroom
2025-08-02 00:57:51 | DEBUG | [DouBaoImageToImage] 命令解析: ['@壹壹.\u2005', '你明天问@饿飞他', '我也不知道他这个打不打']
2025-08-02 00:57:51 | DEBUG | 处理消息内容: '@壹壹.  你明天问@饿飞他 我也不知道他这个打不打'
2025-08-02 00:57:51 | DEBUG | 消息内容 '@壹壹.  你明天问@饿飞他 我也不知道他这个打不打' 不匹配任何命令，忽略
2025-08-02 00:58:25 | DEBUG | 收到消息: {'MsgId': 1559607515, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_if9bozh3yp522:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>啊</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>3715052507572711146</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_1ul5r40nibpn12</chatusr>\n\t\t\t<displayname>昭禾以纯</displayname>\n\t\t\t<content>@壹壹.\u2005 你明天问@饿飞他 我也不知道他这个打不打\u2005</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;839359557&lt;/sequence_id&gt;\n\t&lt;atuserlist&gt;wxid_if9bozh3yp522&lt;/atuserlist&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;148&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_KGR3SWL1|v1_RDnoaxkR&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754067478</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_if9bozh3yp522</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754067512, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>9dc089759486c0f2b1f9696fab0ddf9d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_qvnoXG6W|v1_zWj9tbIP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5716577559687194008, 'MsgSeq': 871419419}
2025-08-02 00:58:25 | DEBUG | 从群聊消息中提取发送者: wxid_if9bozh3yp522
2025-08-02 00:58:25 | DEBUG | 使用已解析的XML处理引用消息
2025-08-02 00:58:25 | INFO | 收到引用消息: 消息ID:1559607515 来自:***********@chatroom 发送人:wxid_if9bozh3yp522 内容:啊 引用类型:1
2025-08-02 00:58:25 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-02 00:58:25 | INFO | [DouBaoImageToImage] 消息内容: '啊' from wxid_if9bozh3yp522 in ***********@chatroom
2025-08-02 00:58:25 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['啊']
2025-08-02 00:58:25 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-02 00:58:25 | INFO | 发送表情消息: 对方wxid:***********@chatroom md5:5a1e188c79cc0c66e11cf127f264b68f 总长度:9992069
2025-08-02 00:58:25 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-02 00:58:25 | INFO |   - 消息内容: 啊
2025-08-02 00:58:25 | INFO |   - 群组ID: ***********@chatroom
2025-08-02 00:58:25 | INFO |   - 发送人: wxid_if9bozh3yp522
2025-08-02 00:58:25 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '@壹壹.\u2005 你明天问@饿飞他 我也不知道他这个打不打\u2005', 'Msgid': '3715052507572711146', 'NewMsgId': '3715052507572711146', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '昭禾以纯', 'MsgSource': '<msgsource><sequence_id>839359557</sequence_id>\n\t<atuserlist>wxid_if9bozh3yp522</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_KGR3SWL1|v1_RDnoaxkR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754067478', 'SenderWxid': 'wxid_if9bozh3yp522'}
2025-08-02 00:58:25 | INFO |   - 引用消息ID: 
2025-08-02 00:58:25 | INFO |   - 引用消息类型: 
2025-08-02 00:58:25 | INFO |   - 引用消息内容: @壹壹.  你明天问@饿飞他 我也不知道他这个打不打 
2025-08-02 00:58:25 | INFO |   - 引用消息发送人: wxid_if9bozh3yp522
2025-08-02 00:58:28 | DEBUG | 收到消息: {'MsgId': 2119801176, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_if9bozh3yp522:\n<msg><emoji fromusername="wxid_if9bozh3yp522" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="97979c70cf1d32630cdde369f1d6ffb0" len="769227" productid="" androidmd5="97979c70cf1d32630cdde369f1d6ffb0" androidlen="769227" s60v3md5="97979c70cf1d32630cdde369f1d6ffb0" s60v3len="769227" s60v5md5="97979c70cf1d32630cdde369f1d6ffb0" s60v5len="769227" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=97979c70cf1d32630cdde369f1d6ffb0&amp;filekey=30440201010430302e02016e0402535a0420393739373963373063663164333236333063646465333639663164366666623002030bbccb040d00000004627466730000000132&amp;hy=SZ&amp;storeid=262c4445d00020c7f24b68acf0000006e01004fb1535a1c29b970b695628ec&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=36bc2c6d04a93b32067ed3a905e21cbd&amp;filekey=30440201010430302e02016e0402535a0420333662633263366430346139336233323036376564336139303565323163626402030bbcd0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=262c4445d0003a08124b68acf0000006e02004fb2535a1c29b970b6956290d&amp;ef=2&amp;bizid=1022" aeskey="041615d6c7b54a2cab6636f277443967" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=924fe1bd4d51c867f54e4b5aedbeb9a4&amp;filekey=30440201010430302e02016e0402535a04203932346665316264346435316338363766353465346235616564626562396134020300e900040d00000004627466730000000132&amp;hy=SZ&amp;storeid=262c4445d000525e024b68acf0000006e03004fb3535a1c29b970b69562934&amp;ef=3&amp;bizid=1022" externmd5="ad4eabb92e0d9b5ab9c4e33ce3740b22" width="324" height="324" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754067515, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_2aiQ3h0p|v1_n1rSGLFr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5222893409695163386, 'MsgSeq': 871419422}
2025-08-02 00:58:28 | INFO | 收到表情消息: 消息ID:2119801176 来自:***********@chatroom 发送人:wxid_if9bozh3yp522 MD5:97979c70cf1d32630cdde369f1d6ffb0 大小:769227
2025-08-02 00:58:28 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5222893409695163386
2025-08-02 00:58:46 | DEBUG | 收到消息: {'MsgId': 833209418, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n<msg><emoji fromusername="wxid_1ul5r40nibpn12" tousername="***********@chatroom" type="3" idbuffer="media:0_0" md5="1ba6ff468dd6b7fef47b49f77c306e41" len="17301" productid="" androidmd5="1ba6ff468dd6b7fef47b49f77c306e41" androidlen="17301" s60v3md5="1ba6ff468dd6b7fef47b49f77c306e41" s60v3len="17301" s60v5md5="1ba6ff468dd6b7fef47b49f77c306e41" s60v5len="17301" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=1ba6ff468dd6b7fef47b49f77c306e41&amp;filekey=3043020101042f302d02016e040253480420316261366666343638646436623766656634376234396637376333303665343102024395040d00000004627466730000000132&amp;hy=SH&amp;storeid=2687601e60009d4193bbd0a890000006e01004fb153481a4321f1568e03541&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=1ee9bc1babc04f174093da8351132ede&amp;filekey=3043020101042f302d02016e0402534804203165653962633162616263303466313734303933646138333531313332656465020243a0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2687601e6000ab1913bbd0a890000006e02004fb253481a4321f1568e03558&amp;ef=2&amp;bizid=1022" aeskey="867721ffdd2b46169dbb966df6410b79" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=77709386f4d8879d5110e4b8f3b1941f&amp;filekey=3043020101042f302d02016e040253480420373737303933383666346438383739643531313065346238663362313934316602021c90040d00000004627466730000000132&amp;hy=SH&amp;storeid=2687601e6000b1e793bbd0a890000006e03004fb353481a4321f1568e03565&amp;ef=3&amp;bizid=1022" externmd5="bc53e378fbe00e4f25424ba4de756691" width="512" height="505" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754067533, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_GIfmRCew|v1_sgxqX3R5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5897861936073576752, 'MsgSeq': 871419423}
2025-08-02 00:58:46 | INFO | 收到表情消息: 消息ID:833209418 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 MD5:1ba6ff468dd6b7fef47b49f77c306e41 大小:17301
2025-08-02 00:58:46 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5897861936073576752
2025-08-02 00:59:21 | DEBUG | 收到消息: {'MsgId': 1774115224, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_if9bozh3yp522:\n没事 明天看'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754067569, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_lS9yQnyk|v1_ukiIxUTU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2209470387037001085, 'MsgSeq': 871419424}
2025-08-02 00:59:21 | INFO | 收到文本消息: 消息ID:1774115224 来自:***********@chatroom 发送人:wxid_if9bozh3yp522 @:[] 内容:没事 明天看
2025-08-02 00:59:21 | DEBUG | [DouBaoImageToImage] 收到文本消息: '没事 明天看' from wxid_if9bozh3yp522 in ***********@chatroom
2025-08-02 00:59:21 | DEBUG | [DouBaoImageToImage] 命令解析: ['没事', '明天看']
2025-08-02 00:59:21 | DEBUG | 处理消息内容: '没事 明天看'
2025-08-02 00:59:21 | DEBUG | 消息内容 '没事 明天看' 不匹配任何命令，忽略
2025-08-02 01:00:45 | DEBUG | 收到消息: {'MsgId': 525432154, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_usdcl6hyaig922:\n六周年扬帆起航\n八月发发发\nR1KLYRKCSL'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754067653, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>4</cf>\n\t\t<inlenlist>24</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_/TngymaY|v1_4Vuu/+B4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1460368495914490745, 'MsgSeq': 871419425}
2025-08-02 01:00:45 | INFO | 收到文本消息: 消息ID:525432154 来自:***********@chatroom 发送人:wxid_usdcl6hyaig922 @:[] 内容:六周年扬帆起航
八月发发发
R1KLYRKCSL
2025-08-02 01:00:45 | DEBUG | [DouBaoImageToImage] 收到文本消息: '六周年扬帆起航
八月发发发
R1KLYRKCSL' from wxid_usdcl6hyaig922 in ***********@chatroom
2025-08-02 01:00:45 | DEBUG | [DouBaoImageToImage] 命令解析: ['六周年扬帆起航\n八月发发发\nR1KLYRKCSL']
2025-08-02 01:00:45 | DEBUG | 处理消息内容: '六周年扬帆起航
八月发发发
R1KLYRKCSL'
2025-08-02 01:00:45 | DEBUG | 消息内容 '六周年扬帆起航
八月发发发
R1KLYRKCSL' 不匹配任何命令，忽略
2025-08-02 01:02:32 | DEBUG | 收到消息: {'MsgId': 2142680163, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n唱舞签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754067759, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_xnyLVumG|v1_mxOekMH3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9209509359069807331, 'MsgSeq': 871419426}
2025-08-02 01:02:32 | INFO | 收到文本消息: 消息ID:2142680163 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:唱舞签到
2025-08-02 01:02:32 | INFO | 发送链接消息: 对方wxid:***********@chatroom 链接:https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx6de432d4ad7e151c&redirect_uri=http%3A%2F%2Freserve.fhsj.xipu.com%2Fapi%2Fsign%2Findex&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect 标题:唱舞全明星 描述:点击进入签到页面，领取专属福利 缩略图链接:https://mmbiz.qpic.cn/mmbiz_jpg/RicuJvxibRtUBlv9G75UTulanktDF1OxFO7Wyhzs1WS609tq1j9icfNhLkM6zB3lwM5ZlbgQia1ibIcxuj35WAm465w/640?wxtype=jpeg&wxfrom=0
2025-08-02 01:02:32 | DEBUG | [DouBaoImageToImage] 收到文本消息: '唱舞签到' from wxid_ugv5ryus4gz622 in ***********@chatroom
2025-08-02 01:02:32 | DEBUG | [DouBaoImageToImage] 命令解析: ['唱舞签到']
2025-08-02 01:02:32 | DEBUG | 处理消息内容: '唱舞签到'
2025-08-02 01:02:32 | DEBUG | 消息内容 '唱舞签到' 不匹配任何命令，忽略
2025-08-02 01:06:03 | DEBUG | 收到消息: {'MsgId': 554959946, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_se4umlaxvz1p21:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="0de59a2481e383126ea65b266aa776a6" encryver="1" cdnthumbaeskey="0de59a2481e383126ea65b266aa776a6" cdnthumburl="3057020100044b30490201000204365d709102032df7950204d45d06af0204688cf402042434353236393437652d663632362d343438622d623239332d306662383936346435363330020405250a020201000405004c50b900" cdnthumblength="3816" cdnthumbheight="228" cdnthumbwidth="502" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204365d709102032df7950204d45d06af0204688cf402042434353236393437652d663632362d343438622d623239332d306662383936346435363330020405250a020201000405004c50b900" length="1103205" md5="4243abe4077bc6b9138ba0cca8b8a84e" hevc_mid_size="91524" originsourcemd5="dd62eed6c364d5bc9eb6ffaad9ad31b8">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjUxMTA1NTAwNDAwMDUxMTAiLCJwZHFIYXNoIjoiNTBjYjVjY2JkMTFjMGNlNWFi\nM2MyZjM0NDQzZGQxM2QyY2MzZjMzNjU2YzNiM2MzYTI1NjVlYzM1MDJmOGNkYyJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754067971, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>e248feeca178c956b147727115f8eb47_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_di0MtDQA|v1_N4VqLRYX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5476823543972130142, 'MsgSeq': 871419429}
2025-08-02 01:06:03 | INFO | 收到图片消息: 消息ID:554959946 来自:***********@chatroom 发送人:wxid_se4umlaxvz1p21 XML:<?xml version="1.0"?><msg><img aeskey="0de59a2481e383126ea65b266aa776a6" encryver="1" cdnthumbaeskey="0de59a2481e383126ea65b266aa776a6" cdnthumburl="3057020100044b30490201000204365d709102032df7950204d45d06af0204688cf402042434353236393437652d663632362d343438622d623239332d306662383936346435363330020405250a020201000405004c50b900" cdnthumblength="3816" cdnthumbheight="228" cdnthumbwidth="502" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204365d709102032df7950204d45d06af0204688cf402042434353236393437652d663632362d343438622d623239332d306662383936346435363330020405250a020201000405004c50b900" length="1103205" md5="4243abe4077bc6b9138ba0cca8b8a84e" hevc_mid_size="91524" originsourcemd5="dd62eed6c364d5bc9eb6ffaad9ad31b8"><secHashInfoBase64>eyJwaGFzaCI6IjUxMTA1NTAwNDAwMDUxMTAiLCJwZHFIYXNoIjoiNTBjYjVjY2JkMTFjMGNlNWFiM2MyZjM0NDQzZGQxM2QyY2MzZjMzNjU2YzNiM2MzYTI1NjVlYzM1MDJmOGNkYyJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-02 01:06:03 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-08-02 01:06:03 | INFO | [TimerTask] 缓存图片消息: 554959946
2025-08-02 01:06:11 | DEBUG | 收到消息: {'MsgId': 1110283725, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_se4umlaxvz1p21:\n黑底算了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754067979, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_1c/C16ab|v1_Ve1WNzJc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7360731611960155425, 'MsgSeq': 871419430}
2025-08-02 01:06:11 | INFO | 收到文本消息: 消息ID:1110283725 来自:***********@chatroom 发送人:wxid_se4umlaxvz1p21 @:[] 内容:黑底算了
2025-08-02 01:06:11 | DEBUG | [DouBaoImageToImage] 收到文本消息: '黑底算了' from wxid_se4umlaxvz1p21 in ***********@chatroom
2025-08-02 01:06:11 | DEBUG | [DouBaoImageToImage] 命令解析: ['黑底算了']
2025-08-02 01:06:11 | DEBUG | 处理消息内容: '黑底算了'
2025-08-02 01:06:11 | DEBUG | 消息内容 '黑底算了' 不匹配任何命令，忽略
2025-08-02 01:07:54 | DEBUG | 收到消息: {'MsgId': 1560909186, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_if9bozh3yp522:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>这是什么</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>1460368495914490745</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_usdcl6hyaig922</chatusr>\n\t\t\t<displayname>奥利ào</displayname>\n\t\t\t<content>六周年扬帆起航\n八月发发发\nR1KLYRKCSL</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;839359578&lt;/sequence_id&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;alnode&gt;\n\t\t&lt;cf&gt;4&lt;/cf&gt;\n\t\t&lt;inlenlist&gt;24&lt;/inlenlist&gt;\n\t&lt;/alnode&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;148&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_qluRby17|v1_WVnXFFVA&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754067653</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_if9bozh3yp522</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754068081, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>b90c6cfd77a422864f666618f8df2480_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_qdvaCMfW|v1_zmMg5Axs</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8192452058173976728, 'MsgSeq': 871419431}
2025-08-02 01:07:54 | DEBUG | 从群聊消息中提取发送者: wxid_if9bozh3yp522
2025-08-02 01:07:54 | DEBUG | 使用已解析的XML处理引用消息
2025-08-02 01:07:54 | INFO | 收到引用消息: 消息ID:1560909186 来自:***********@chatroom 发送人:wxid_if9bozh3yp522 内容:这是什么 引用类型:1
2025-08-02 01:07:54 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-02 01:07:54 | INFO | [DouBaoImageToImage] 消息内容: '这是什么' from wxid_if9bozh3yp522 in ***********@chatroom
2025-08-02 01:07:54 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['这是什么']
2025-08-02 01:07:54 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-02 01:07:54 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-02 01:07:54 | INFO |   - 消息内容: 这是什么
2025-08-02 01:07:54 | INFO |   - 群组ID: ***********@chatroom
2025-08-02 01:07:54 | INFO |   - 发送人: wxid_if9bozh3yp522
2025-08-02 01:07:54 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '六周年扬帆起航\n八月发发发\nR1KLYRKCSL', 'Msgid': '1460368495914490745', 'NewMsgId': '1460368495914490745', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '奥利ào', 'MsgSource': '<msgsource><sequence_id>839359578</sequence_id>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>4</cf>\n\t\t<inlenlist>24</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_qluRby17|v1_WVnXFFVA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754067653', 'SenderWxid': 'wxid_if9bozh3yp522'}
2025-08-02 01:07:54 | INFO |   - 引用消息ID: 
2025-08-02 01:07:54 | INFO |   - 引用消息类型: 
2025-08-02 01:07:54 | INFO |   - 引用消息内容: 六周年扬帆起航
八月发发发
R1KLYRKCSL
2025-08-02 01:07:54 | INFO |   - 引用消息发送人: wxid_if9bozh3yp522
2025-08-02 01:09:08 | DEBUG | 收到消息: {'MsgId': 1271543471, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_q8zwlhoeazkc22:\n礼包码'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754068155, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_Hpk9r8md|v1_+tMbE0Hi</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2774936359000812209, 'MsgSeq': 871419432}
2025-08-02 01:09:08 | INFO | 收到文本消息: 消息ID:1271543471 来自:***********@chatroom 发送人:wxid_q8zwlhoeazkc22 @:[] 内容:礼包码
2025-08-02 01:09:08 | DEBUG | [DouBaoImageToImage] 收到文本消息: '礼包码' from wxid_q8zwlhoeazkc22 in ***********@chatroom
2025-08-02 01:09:08 | DEBUG | [DouBaoImageToImage] 命令解析: ['礼包码']
2025-08-02 01:09:08 | DEBUG | 处理消息内容: '礼包码'
2025-08-02 01:09:08 | DEBUG | 消息内容 '礼包码' 不匹配任何命令，忽略
2025-08-02 01:09:12 | DEBUG | 收到消息: {'MsgId': 1034415174, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_q8zwlhoeazkc22:\n<msg><emoji fromusername="wxid_q8zwlhoeazkc22" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="3f8ee48e3fb75f25b0e6ad5c0a639f61" len="980486" productid="" androidmd5="3f8ee48e3fb75f25b0e6ad5c0a639f61" androidlen="980486" s60v3md5="3f8ee48e3fb75f25b0e6ad5c0a639f61" s60v3len="980486" s60v5md5="3f8ee48e3fb75f25b0e6ad5c0a639f61" s60v5len="980486" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=3f8ee48e3fb75f25b0e6ad5c0a639f61&amp;filekey=30440201010430302e02016e0402535a0420336638656534386533666237356632356230653661643563306136333966363102030ef606040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26342c028000a767a45ddebde0000006e01004fb1535a02da2970b7b4ac550&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=3ae4335923fdbd12e20fdb5afea1f4ec&amp;filekey=30440201010430302e02016e0402535a0420336165343333353932336664626431326532306664623561666561316634656302030ef610040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26342c028000c910c45ddebde0000006e02004fb2535a02da2970b7b4ac58c&amp;ef=2&amp;bizid=1022" aeskey="23fb7c3295eb4f7bab477f30d1a475e2" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=b9e68820016141ddd484f2cc4e64e251&amp;filekey=30440201010430302e02016e0402535a04206239653638383230303136313431646464343834663263633465363465323531020300d6d0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26342c028000e75ce45ddebde0000006e03004fb3535a02da2970b7b4ac5c9&amp;ef=3&amp;bizid=1022" externmd5="95d3e8c0dabe2b513d4bed7c2d678de1" width="317" height="317" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754068160, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_EbSgq1/+|v1_6f0y5noJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2447028972284152327, 'MsgSeq': 871419433}
2025-08-02 01:09:12 | INFO | 收到表情消息: 消息ID:1034415174 来自:***********@chatroom 发送人:wxid_q8zwlhoeazkc22 MD5:3f8ee48e3fb75f25b0e6ad5c0a639f61 大小:980486
2025-08-02 01:09:12 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2447028972284152327
2025-08-02 01:12:46 | DEBUG | 收到消息: {'MsgId': 732981149, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_usdcl6hyaig922:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="72d5ab8cd38bf70a235f43651d4d4bca" encryver="1" cdnthumbaeskey="72d5ab8cd38bf70a235f43651d4d4bca" cdnthumburl="3057020100044b30490201000204a1b7f53602032df1ba02048347dd3c0204688cf2bf042461306234646533392d306630342d346638382d396364382d653964356434663639363632020405290a020201000405004c57c200" cdnthumblength="3790" cdnthumbheight="238" cdnthumbwidth="524" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204a1b7f53602032df1ba02048347dd3c0204688cf2bf042461306234646533392d306630342d346638382d396364382d653964356434663639363632020405290a020201000405004c57c200" length="943862" md5="96bf6a3094501ea681619a7a4501e9f2" hevc_mid_size="62968" originsourcemd5="ae0fba357af2ae47d9e8c35c0e3b0568">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjcwMDA1NTAwMDU1MDUwNTAiLCJwZHFIYXNoIjoiNDY2MjdiOWM3ODZiNjA2Njg0\nMWE4NjY2Yjk5OTc4MTlmOWY5NzlmOTI2NjYxMTllYzY2Mzg2MjEwNzllNzZlNyJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754068374, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>44b25bdc2a845f4af52118eebb020f1b_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_FMRhLlr3|v1_EhvdOaEx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5500388208467303302, 'MsgSeq': 871419434}
2025-08-02 01:12:46 | INFO | 收到图片消息: 消息ID:732981149 来自:***********@chatroom 发送人:wxid_usdcl6hyaig922 XML:<?xml version="1.0"?><msg><img aeskey="72d5ab8cd38bf70a235f43651d4d4bca" encryver="1" cdnthumbaeskey="72d5ab8cd38bf70a235f43651d4d4bca" cdnthumburl="3057020100044b30490201000204a1b7f53602032df1ba02048347dd3c0204688cf2bf042461306234646533392d306630342d346638382d396364382d653964356434663639363632020405290a020201000405004c57c200" cdnthumblength="3790" cdnthumbheight="238" cdnthumbwidth="524" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204a1b7f53602032df1ba02048347dd3c0204688cf2bf042461306234646533392d306630342d346638382d396364382d653964356434663639363632020405290a020201000405004c57c200" length="943862" md5="96bf6a3094501ea681619a7a4501e9f2" hevc_mid_size="62968" originsourcemd5="ae0fba357af2ae47d9e8c35c0e3b0568"><secHashInfoBase64>eyJwaGFzaCI6IjcwMDA1NTAwMDU1MDUwNTAiLCJwZHFIYXNoIjoiNDY2MjdiOWM3ODZiNjA2Njg0MWE4NjY2Yjk5OTc4MTlmOWY5NzlmOTI2NjYxMTllYzY2Mzg2MjEwNzllNzZlNyJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-02 01:12:46 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-08-02 01:12:46 | INFO | [TimerTask] 缓存图片消息: 732981149
2025-08-02 01:13:07 | DEBUG | 收到消息: {'MsgId': 826684342, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_usdcl6hyaig922:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>开出来的</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>1460368495914490745</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_usdcl6hyaig922</chatusr>\n\t\t\t<displayname>奥利ào</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;pua&gt;1&lt;/pua&gt;&lt;eggSeed&gt;1855132433&lt;/eggSeed&gt;&lt;alnode&gt;&lt;cf&gt;4&lt;/cf&gt;&lt;inlenlist&gt;24&lt;/inlenlist&gt;&lt;/alnode&gt;&lt;/msgsource&gt;</msgsource>\n\t\t\t<content>六周年扬帆起航\n八月发发发\nR1KLYRKCSL</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754067653</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_usdcl6hyaig922</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754068394, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>768e78713cd65dbbb1a99e8ad53f7c5c_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_XOIYgYsz|v1_Anb3gtYY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6403548487928108551, 'MsgSeq': 871419435}
2025-08-02 01:13:07 | DEBUG | 从群聊消息中提取发送者: wxid_usdcl6hyaig922
2025-08-02 01:13:07 | DEBUG | 使用已解析的XML处理引用消息
2025-08-02 01:13:07 | INFO | 收到引用消息: 消息ID:826684342 来自:***********@chatroom 发送人:wxid_usdcl6hyaig922 内容:开出来的 引用类型:1
2025-08-02 01:13:07 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-02 01:13:07 | INFO | [DouBaoImageToImage] 消息内容: '开出来的' from wxid_usdcl6hyaig922 in ***********@chatroom
2025-08-02 01:13:07 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['开出来的']
2025-08-02 01:13:07 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-02 01:13:07 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-02 01:13:07 | INFO |   - 消息内容: 开出来的
2025-08-02 01:13:07 | INFO |   - 群组ID: ***********@chatroom
2025-08-02 01:13:07 | INFO |   - 发送人: wxid_usdcl6hyaig922
2025-08-02 01:13:07 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '六周年扬帆起航\n八月发发发\nR1KLYRKCSL', 'Msgid': '1460368495914490745', 'NewMsgId': '1460368495914490745', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '奥利ào', 'MsgSource': '<msgsource><pua>1</pua><eggSeed>1855132433</eggSeed><alnode><cf>4</cf><inlenlist>24</inlenlist></alnode></msgsource>', 'Createtime': '1754067653', 'SenderWxid': 'wxid_usdcl6hyaig922'}
2025-08-02 01:13:07 | INFO |   - 引用消息ID: 
2025-08-02 01:13:07 | INFO |   - 引用消息类型: 
2025-08-02 01:13:07 | INFO |   - 引用消息内容: 六周年扬帆起航
八月发发发
R1KLYRKCSL
2025-08-02 01:13:07 | INFO |   - 引用消息发送人: wxid_usdcl6hyaig922
2025-08-02 01:19:41 | DEBUG | 收到消息: {'MsgId': 1051017083, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_if9bozh3yp522:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>emmm 哈哈 好的</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>2774936359000812209</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_q8zwlhoeazkc22</chatusr>\n\t\t\t<displayname>一枝鱼</displayname>\n\t\t\t<content>礼包码</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;839359594&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;148&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_YE4K1zZh|v1_jC6TIa5T&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754068155</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_if9bozh3yp522</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754068789, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>fda836c8b857e078a7a0a9673d2b1028_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_N9oj+Gzl|v1_zasuBMcd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7939801388946904869, 'MsgSeq': 871419436}
2025-08-02 01:19:41 | DEBUG | 从群聊消息中提取发送者: wxid_if9bozh3yp522
2025-08-02 01:19:41 | DEBUG | 使用已解析的XML处理引用消息
2025-08-02 01:19:41 | INFO | 收到引用消息: 消息ID:1051017083 来自:***********@chatroom 发送人:wxid_if9bozh3yp522 内容:emmm 哈哈 好的 引用类型:1
2025-08-02 01:19:41 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-02 01:19:41 | INFO | [DouBaoImageToImage] 消息内容: 'emmm 哈哈 好的' from wxid_if9bozh3yp522 in ***********@chatroom
2025-08-02 01:19:41 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['emmm', '哈哈', '好的']
2025-08-02 01:19:41 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-02 01:19:41 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-02 01:19:41 | INFO |   - 消息内容: emmm 哈哈 好的
2025-08-02 01:19:41 | INFO |   - 群组ID: ***********@chatroom
2025-08-02 01:19:41 | INFO |   - 发送人: wxid_if9bozh3yp522
2025-08-02 01:19:41 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '礼包码', 'Msgid': '2774936359000812209', 'NewMsgId': '2774936359000812209', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '一枝鱼', 'MsgSource': '<msgsource><sequence_id>839359594</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_YE4K1zZh|v1_jC6TIa5T</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754068155', 'SenderWxid': 'wxid_if9bozh3yp522'}
2025-08-02 01:19:42 | INFO |   - 引用消息ID: 
2025-08-02 01:19:42 | INFO |   - 引用消息类型: 
2025-08-02 01:19:42 | INFO |   - 引用消息内容: 礼包码
2025-08-02 01:19:42 | INFO |   - 引用消息发送人: wxid_if9bozh3yp522
2025-08-02 01:23:43 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-02 01:23:43 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-08-02 01:23:45 | INFO | 发送链接消息: 对方wxid:***********@chatroom 链接:weixin:// 标题:👤 群成员改名提醒 描述:💫 群友换新装扮啦~
👉 原昵称：Justོི
👉 新昵称：XvemiZ¹⁴ 缩略图链接:https://wx.qlogo.cn/mmhead/ver_1/1qJTKTaaOAD5VzUGdGeickNuJ1CDUSJycotAYvTErnjuQFVTBEuhgSPwoL414w2dJ2Jiciavwq7r0DXLku1sYB4S2xkDnQMAlbrAv4EMmKDqS8D2fia1HibCKJZ9a6rTkibUr4/132
2025-08-02 01:23:45 | INFO | 群 ***********@chatroom 发现 1 个成员改名
2025-08-02 01:23:45 | DEBUG | 群成员变化检查完成
2025-08-02 01:52:59 | DEBUG | 收到消息: {'MsgId': 1488700220, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cd7sofqy727s21:\n以后得多存点钻石 不然到时候又要消费钻石拿不出来[捂脸]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754070786, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_Ls0JuoR6|v1_qqDDpw4E</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9219594532510703525, 'MsgSeq': 871419439}
2025-08-02 01:52:59 | INFO | 收到文本消息: 消息ID:1488700220 来自:***********@chatroom 发送人:wxid_cd7sofqy727s21 @:[] 内容:以后得多存点钻石 不然到时候又要消费钻石拿不出来[捂脸]
2025-08-02 01:52:59 | DEBUG | [DouBaoImageToImage] 收到文本消息: '以后得多存点钻石 不然到时候又要消费钻石拿不出来[捂脸]' from wxid_cd7sofqy727s21 in ***********@chatroom
2025-08-02 01:52:59 | DEBUG | [DouBaoImageToImage] 命令解析: ['以后得多存点钻石', '不然到时候又要消费钻石拿不出来[捂脸]']
2025-08-02 01:52:59 | DEBUG | 处理消息内容: '以后得多存点钻石 不然到时候又要消费钻石拿不出来[捂脸]'
2025-08-02 01:52:59 | DEBUG | 消息内容 '以后得多存点钻石 不然到时候又要消费钻石拿不出来[捂脸]' 不匹配任何命令，忽略
2025-08-02 01:53:43 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-02 02:23:09 | DEBUG | 收到消息: {'MsgId': 1294746764, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\nyes'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754072596, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_qhAIB70H|v1_Cpk1qxQe</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1729595930485090755, 'MsgSeq': 871419440}
2025-08-02 02:23:09 | INFO | 收到文本消息: 消息ID:1294746764 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:yes
2025-08-02 02:23:09 | DEBUG | [DouBaoImageToImage] 收到文本消息: 'yes' from wxid_1ul5r40nibpn12 in ***********@chatroom
2025-08-02 02:23:09 | DEBUG | [DouBaoImageToImage] 命令解析: ['yes']
2025-08-02 02:23:09 | DEBUG | 处理消息内容: 'yes'
2025-08-02 02:23:09 | DEBUG | 消息内容 'yes' 不匹配任何命令，忽略
2025-08-02 02:23:43 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-02 02:23:43 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-08-02 02:23:45 | INFO | 发送链接消息: 对方wxid:***********@chatroom 链接:weixin:// 标题:👤 群成员改名提醒 描述:💫 群友换新装扮啦~
👉 原昵称：奥利ào
👉 新昵称：黎芙 缩略图链接:https://wx.qlogo.cn/mmhead/ver_1/kD9jhkIxuGRApWuGybBvhI2ichtt9ia7QubrFwibezdFHPckzQS41qIJBqqAhqh7DbkykRDZu3sTgO3YUWtibibqCF8tmNH5ZjH53xTxLxGlYu8qLmNPpmnx6yvJS5HbzuBMN/132
2025-08-02 02:23:45 | INFO | 群 ***********@chatroom 发现 1 个成员改名
2025-08-02 02:23:45 | DEBUG | 群成员变化检查完成
2025-08-02 02:53:43 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-02 02:58:14 | DEBUG | 收到消息: {'MsgId': 227555434, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'qianting1731076232:\n签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754074702, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_RSOukZ8n|v1_+P0hwDFt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2032903845571967843, 'MsgSeq': 871419443}
2025-08-02 02:58:14 | INFO | 收到文本消息: 消息ID:227555434 来自:***********@chatroom 发送人:qianting1731076232 @:[] 内容:签到
2025-08-02 02:58:14 | DEBUG | [DouBaoImageToImage] 收到文本消息: '签到' from qianting1731076232 in ***********@chatroom
2025-08-02 02:58:14 | DEBUG | [DouBaoImageToImage] 命令解析: ['签到']
2025-08-02 02:58:14 | DEBUG | 处理消息内容: '签到'
2025-08-02 02:58:14 | DEBUG | 消息内容 '签到' 不匹配任何命令，忽略
2025-08-02 02:58:14 | INFO | 发送表情消息: 对方wxid:***********@chatroom md5:ae672c700aaf271a151e18a9ecf4445b 总长度:30259
2025-08-02 02:58:14 | INFO | 数据库: 用户qianting1731076232登录时间设置为2025-08-02 00:00:00+08:00
2025-08-02 02:58:14 | INFO | 数据库: 用户qianting1731076232连续签到天数设置为10
2025-08-02 02:58:14 | INFO | 数据库: 用户qianting1731076232积分增加15
2025-08-02 02:58:16 | INFO | 发送文字消息: 对方wxid:***********@chatroom at:['qianting1731076232'] 内容:@奈斯༩༧ 
-----XYBot-----
签到成功！你领到了 13 个积分！✅
你是今天第 1 个签到的！🎉
你连续签到了 10 天！ 再奖励 2 积分！[爱心]
2025-08-02 03:23:43 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-02 03:23:43 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-08-02 03:23:44 | DEBUG | 群成员变化检查完成
2025-08-02 03:53:43 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-02 04:23:43 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-02 04:23:43 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-08-02 04:23:44 | DEBUG | 群成员变化检查完成
2025-08-02 04:53:43 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-02 05:23:43 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-02 05:23:43 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-08-02 05:23:44 | DEBUG | 群成员变化检查完成
2025-08-02 05:53:43 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-02 06:19:53 | DEBUG | 收到消息: {'MsgId': 800786154, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_zbh5p28da1si22:\n<msg><emoji fromusername = "wxid_zbh5p28da1si22" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="2fc90db9a8ce8ce58cf32f45cc09df95" len = "349244" productid="com.tencent.xin.emoticon.person.stiker_1589830571a4a0bd1531e7852c" androidmd5="2fc90db9a8ce8ce58cf32f45cc09df95" androidlen="349244" s60v3md5 = "2fc90db9a8ce8ce58cf32f45cc09df95" s60v3len="349244" s60v5md5 = "2fc90db9a8ce8ce58cf32f45cc09df95" s60v5len="349244" cdnurl = "http://mmbiz.qpic.cn/mmemoticon/ajNVdqHZLLAszSAQ4gJlh7kuCCdMre1dsXfbKdU4USicKVszmicukDvJ8OazJ8XQdR/0" designerid = "" thumburl = "http://mmbiz.qpic.cn/mmemoticon/ajNVdqHZLLAszSAQ4gJlh3LKSzIwCLqjbGs90xo4Q7tf3RbWeCH8ETa0qqI0a3pb/0" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=78e1f764879779ab5bc3ecef84e0fe53&amp;filekey=30350201010421301f0202010604025348041078e1f764879779ab5bc3ecef84e0fe530203055440040d00000004627466730000000132&amp;hy=SH&amp;storeid=26308db0c000296ef000000000000010600004f5053482ba67b40b6da9e030&amp;bizid=1023" aeskey= "22ce9f65a4121f19b1bfa55cfaab9fa0" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=0b0f8b6345c2d19b646e6de28e661db3&amp;filekey=30350201010421301f020201060402534804100b0f8b6345c2d19b646e6de28e661db3020300e800040d00000004627466730000000132&amp;hy=SH&amp;storeid=26308db0c0005be8d000000000000010600004f5053482ba67b40b6da9e0c5&amp;bizid=1023" externmd5 = "27d39159f3ced50efc7557fa7fb9d72d" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "CgwKBXpoX2NuEgPml6kKCQoFemhfdHcSAAoLCgdkZWZhdWx0EgA=" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754086802, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_5fP27I8i|v1_Pav+OlaN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1518077494753030556, 'MsgSeq': 871419448}
2025-08-02 06:19:53 | INFO | 收到表情消息: 消息ID:800786154 来自:***********@chatroom 发送人:wxid_zbh5p28da1si22 MD5:2fc90db9a8ce8ce58cf32f45cc09df95 大小:349244
2025-08-02 06:19:53 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1518077494753030556
2025-08-02 06:23:43 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-02 06:23:43 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-08-02 06:23:44 | DEBUG | 群成员变化检查完成
2025-08-02 06:24:55 | DEBUG | 收到消息: {'MsgId': 631588000, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_yxdvl6zp4er522:\n我老眼昏花了吗，游戏都掉了？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754087103, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_31m1j3Qu|v1_ondgPkMe</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6975420443047710291, 'MsgSeq': 871419449}
2025-08-02 06:24:55 | INFO | 收到文本消息: 消息ID:631588000 来自:***********@chatroom 发送人:wxid_yxdvl6zp4er522 @:[] 内容:我老眼昏花了吗，游戏都掉了？
2025-08-02 06:24:55 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我老眼昏花了吗，游戏都掉了？' from wxid_yxdvl6zp4er522 in ***********@chatroom
2025-08-02 06:24:55 | DEBUG | [DouBaoImageToImage] 命令解析: ['我老眼昏花了吗，游戏都掉了？']
2025-08-02 06:24:55 | DEBUG | 处理消息内容: '我老眼昏花了吗，游戏都掉了？'
2025-08-02 06:24:55 | DEBUG | 消息内容 '我老眼昏花了吗，游戏都掉了？' 不匹配任何命令，忽略
2025-08-02 06:34:34 | DEBUG | 收到消息: {'MsgId': 1754087460, 'FromUserName': {'string': 'newsapp'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '<?xml version="1.0"?>\n<sysmsg type="functionmsg">\n\t<functionmsg>\n\t\t<cgi>/cgi-bin/micromsg-bin/addtxnewsmsg</cgi>\n\t\t<cmdid>825</cmdid>\n\t\t<businessid>50001</businessid>\n\t\t<functionmsgid>2025080200</functionmsgid>\n\t\t<op>0</op>\n\t\t<version>1754087415</version>\n\t\t<retryinterval>150</retryinterval>\n\t\t<reportid>63162</reportid>\n\t\t<successkey>0</successkey>\n\t\t<failkey>1</failkey>\n\t\t<finalfailkey>2</finalfailkey>\n\t\t<custombuff>CAAQ\nAzj4/7TEBkD3/7TEBkikgLXEBlAA</custombuff>\n\t\t<retrycount>3</retrycount>\n\t</functionmsg>\n</sysmsg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754087682, 'NewMsgId': 1754087460, 'MsgSeq': 0}
2025-08-02 06:34:34 | DEBUG | 系统消息类型: functionmsg
2025-08-02 06:34:34 | INFO | 未知的系统消息类型: {'MsgId': 1754087460, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '<?xml version="1.0"?>\n<sysmsg type="functionmsg">\n\t<functionmsg>\n\t\t<cgi>/cgi-bin/micromsg-bin/addtxnewsmsg</cgi>\n\t\t<cmdid>825</cmdid>\n\t\t<businessid>50001</businessid>\n\t\t<functionmsgid>2025080200</functionmsgid>\n\t\t<op>0</op>\n\t\t<version>1754087415</version>\n\t\t<retryinterval>150</retryinterval>\n\t\t<reportid>63162</reportid>\n\t\t<successkey>0</successkey>\n\t\t<failkey>1</failkey>\n\t\t<finalfailkey>2</finalfailkey>\n\t\t<custombuff>CAAQ\nAzj4/7TEBkD3/7TEBkikgLXEBlAA</custombuff>\n\t\t<retrycount>3</retrycount>\n\t</functionmsg>\n</sysmsg>\n', 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754087682, 'NewMsgId': 1754087460, 'MsgSeq': 0, 'FromWxid': 'newsapp', 'SenderWxid': 'newsapp', 'IsGroup': False}
2025-08-02 06:35:04 | DEBUG | 收到消息: {'MsgId': 1607071942, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'qq631390473:\n没花，掉多正常'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754087712, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_6dkq72XD|v1_G/Y0mgwl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 364035812048506403, 'MsgSeq': 871419450}
2025-08-02 06:35:04 | INFO | 收到文本消息: 消息ID:1607071942 来自:***********@chatroom 发送人:qq631390473 @:[] 内容:没花，掉多正常
2025-08-02 06:35:04 | DEBUG | [DouBaoImageToImage] 收到文本消息: '没花，掉多正常' from qq631390473 in ***********@chatroom
2025-08-02 06:35:04 | DEBUG | [DouBaoImageToImage] 命令解析: ['没花，掉多正常']
2025-08-02 06:35:04 | DEBUG | 处理消息内容: '没花，掉多正常'
2025-08-02 06:35:04 | DEBUG | 消息内容 '没花，掉多正常' 不匹配任何命令，忽略
2025-08-02 06:36:16 | DEBUG | 收到消息: {'MsgId': 1018274507, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n陪妈妈买菜'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754087784, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_2HbCeEwN|v1_CEi4VCfg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3744738449318090616, 'MsgSeq': 871419451}
2025-08-02 06:36:16 | INFO | 收到文本消息: 消息ID:1018274507 来自:***********@chatroom 发送人:wxid_zbh5p28da1si22 @:[] 内容:陪妈妈买菜
2025-08-02 06:36:16 | DEBUG | [DouBaoImageToImage] 收到文本消息: '陪妈妈买菜' from wxid_zbh5p28da1si22 in ***********@chatroom
2025-08-02 06:36:16 | DEBUG | [DouBaoImageToImage] 命令解析: ['陪妈妈买菜']
2025-08-02 06:36:16 | DEBUG | 处理消息内容: '陪妈妈买菜'
2025-08-02 06:36:16 | DEBUG | 消息内容 '陪妈妈买菜' 不匹配任何命令，忽略
2025-08-02 06:37:06 | DEBUG | 收到消息: {'MsgId': 743281483, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'qq631390473:\n<msg><emoji fromusername = "qq631390473" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="ecaa3e24869591aacc50091218e765f9" len = "688598" productid="" androidmd5="ecaa3e24869591aacc50091218e765f9" androidlen="688598" s60v3md5 = "ecaa3e24869591aacc50091218e765f9" s60v3len="688598" s60v5md5 = "ecaa3e24869591aacc50091218e765f9" s60v5len="688598" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=ecaa3e24869591aacc50091218e765f9&amp;filekey=30440201010430302e02016e0402535a0420656361613365323438363935393161616363353030393132313865373635663902030a81d6040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313130323931303035343630303063333434366265303761353762656135363838303930303030303036653031303034666231&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=3b93baa3bebaa88a6b779b39e2f8eda8&amp;filekey=30440201010430302e02016e0402535a0420336239336261613362656261613838613662373739623339653266386564613802030a81e0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313130323931303035343630303066303866666265303761353762656135363838303930303030303036653032303034666232&amp;ef=2&amp;bizid=1022" aeskey= "b12cac16fb6e408db404d0213921ab6a" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=a579b0374a2c0aa84f640769677e113b&amp;filekey=30440201010430302e02016e0402535a04206135373962303337346132633061613834663634303736393637376531313362020300f640040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313130323931303035343730303033353237366265303761353762656135363838303930303030303036653033303034666233&amp;ef=3&amp;bizid=1022" externmd5 = "32f765f5462908b1333bc4ddc001260c" width= "331" height= "331" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754087834, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_fu4gxnyq|v1_6RSCDAgN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8536114627894657533, 'MsgSeq': 871419452}
2025-08-02 06:37:06 | INFO | 收到表情消息: 消息ID:743281483 来自:***********@chatroom 发送人:qq631390473 MD5:ecaa3e24869591aacc50091218e765f9 大小:688598
2025-08-02 06:37:06 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8536114627894657533
2025-08-02 06:37:15 | DEBUG | 收到消息: {'MsgId': 1595123792, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'qq631390473:\n<msg><emoji fromusername = "qq631390473" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="d2f3091fb9866880a2bfafa8875f4e68" len = "69579" productid="com.tencent.xin.emoticon.person.stiker_1721610378662bac4aa0ab0fc0" androidmd5="d2f3091fb9866880a2bfafa8875f4e68" androidlen="69579" s60v3md5 = "d2f3091fb9866880a2bfafa8875f4e68" s60v3len="69579" s60v5md5 = "d2f3091fb9866880a2bfafa8875f4e68" s60v5len="69579" cdnurl = "http://wxapp.tc.qq.com/275/20304/stodownload?m=d2f3091fb9866880a2bfafa8875f4e68&amp;filekey=30350201010421301f02020113040253480410d2f3091fb9866880a2bfafa8875f4e680203010fcb040d00000004627466730000000132&amp;hy=SH&amp;storeid=2669f468a00074187b475d3220000011300004f5053480db89bc1e6a18c220&amp;bizid=1023" designerid = "" thumburl = "http://wxapp.tc.qq.com/275/20304/stodownload?m=43d26c77bdfa4b48a82b6d09933f7b98&amp;filekey=30340201010420301e0202011304025348041043d26c77bdfa4b48a82b6d09933f7b980202559b040d00000004627466730000000132&amp;hy=SH&amp;storeid=2669f468a00059002b475d3220000011300004f5053481133b031573885090&amp;bizid=1023" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=49336eedd7b5bd517486c7cf301157fa&amp;filekey=30350201010421301f0202010604025348041049336eedd7b5bd517486c7cf301157fa0203010fd0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2669f468a000e4752b475d3220000010600004f50534816c8cbc1e6fae0d3c&amp;bizid=1023" aeskey= "307ebdb8ebadf0874fba2846216ea390" externurl = "" externmd5 = "" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "ChQKB2RlZmF1bHQSCee7p+e7reedoQ==" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754087843, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_nr4CvvXh|v1_m0TFRVJf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2591333374561018623, 'MsgSeq': 871419453}
2025-08-02 06:37:15 | INFO | 收到表情消息: 消息ID:1595123792 来自:***********@chatroom 发送人:qq631390473 MD5:d2f3091fb9866880a2bfafa8875f4e68 大小:69579
2025-08-02 06:37:15 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2591333374561018623
2025-08-02 06:41:37 | DEBUG | 收到消息: {'MsgId': 1743917013, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n早早早'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754088105, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_+uMis1Fw|v1_w33y9qX+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 早早早', 'NewMsgId': 6142351989543651149, 'MsgSeq': 871419454}
2025-08-02 06:41:37 | INFO | 收到文本消息: 消息ID:1743917013 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:早早早
2025-08-02 06:41:38 | DEBUG | [DouBaoImageToImage] 收到文本消息: '早早早' from wxid_wlnzvr8ivgd422 in 48097389945@chatroom
2025-08-02 06:41:38 | DEBUG | [DouBaoImageToImage] 命令解析: ['早早早']
2025-08-02 06:41:38 | DEBUG | 处理消息内容: '早早早'
2025-08-02 06:41:38 | DEBUG | 消息内容 '早早早' 不匹配任何命令，忽略
2025-08-02 06:42:07 | DEBUG | 收到消息: {'MsgId': 666032675, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n洞房'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754088135, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_uLTFzQVb|v1_hNi4m9Ex</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 洞房', 'NewMsgId': 499357031203609484, 'MsgSeq': 871419455}
2025-08-02 06:42:07 | INFO | 收到文本消息: 消息ID:666032675 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:洞房
2025-08-02 06:42:08 | DEBUG | [DouBaoImageToImage] 收到文本消息: '洞房' from wxid_wlnzvr8ivgd422 in 48097389945@chatroom
2025-08-02 06:42:08 | DEBUG | [DouBaoImageToImage] 命令解析: ['洞房']
2025-08-02 06:42:08 | DEBUG | 处理消息内容: '洞房'
2025-08-02 06:42:08 | DEBUG | 消息内容 '洞房' 不匹配任何命令，忽略
2025-08-02 06:42:09 | DEBUG | 收到消息: {'MsgId': 787853922, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_q35rkzgkjvlv12:\n 💕 [锦岚]👩\u200d❤\u200d👨[小爱]💕锦岚你刚刚买的兰博基尼[砖圈]带上最爱你的小爱，去旅游\n💓═☘︎═•洞💗房•═☘︎═💓\n👩\u200d❤\u200d👨关系：恩爱夫妻\n⛺地点：厕所\n😍活动：搞一圈\n[爱心]结果：依旧兼听\n💓═☘︎══•💗•══☘︎═💓\n🔮魅力:+47\n[烟花]恩爱:+10 \n🕒下次:2025-08-02 07:02:17'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754088137, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_vGurnk5A|v1_XHXFusqr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '摇月 :  \ue327 [锦岚]\ue005\u200d\ue022\u200d\ue004[小爱]\ue327锦岚你刚刚买的兰博基尼[砖圈]带上...', 'NewMsgId': 2347809701305534575, 'MsgSeq': 871419456}
2025-08-02 06:42:09 | INFO | 收到文本消息: 消息ID:787853922 来自:48097389945@chatroom 发送人:wxid_q35rkzgkjvlv12 @:[] 内容: 💕 [锦岚]👩‍❤‍👨[小爱]💕锦岚你刚刚买的兰博基尼[砖圈]带上最爱你的小爱，去旅游
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：恩爱夫妻
⛺地点：厕所
😍活动：搞一圈
[爱心]结果：依旧兼听
💓═☘︎══•💗•══☘︎═💓
🔮魅力:+47
[烟花]恩爱:+10 
🕒下次:2025-08-02 07:02:17
2025-08-02 06:42:10 | DEBUG | [DouBaoImageToImage] 收到文本消息: '💕 [锦岚]👩‍❤‍👨[小爱]💕锦岚你刚刚买的兰博基尼[砖圈]带上最爱你的小爱，去旅游
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：恩爱夫妻
⛺地点：厕所
😍活动：搞一圈
[爱心]结果：依旧兼听
💓═☘︎══•💗•══☘︎═💓
🔮魅力:+47
[烟花]恩爱:+10 
🕒下次:2025-08-02 07:02:17' from wxid_q35rkzgkjvlv12 in 48097389945@chatroom
2025-08-02 06:42:10 | DEBUG | [DouBaoImageToImage] 命令解析: ['💕', '[锦岚]👩\u200d❤\u200d👨[小爱]💕锦岚你刚刚买的兰博基尼[砖圈]带上最爱你的小爱，去旅游\n💓═☘︎═•洞💗房•═☘︎═💓\n👩\u200d❤\u200d👨关系：恩爱夫妻\n⛺地点：厕所\n😍活动：搞一圈\n[爱心]结果：依旧兼听\n💓═☘︎══•💗•══☘︎═💓\n🔮魅力:+47\n[烟花]恩爱:+10', '\n🕒下次:2025-08-02 07:02:17']
2025-08-02 06:42:10 | DEBUG | 处理消息内容: '💕 [锦岚]👩‍❤‍👨[小爱]💕锦岚你刚刚买的兰博基尼[砖圈]带上最爱你的小爱，去旅游
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：恩爱夫妻
⛺地点：厕所
😍活动：搞一圈
[爱心]结果：依旧兼听
💓═☘︎══•💗•══☘︎═💓
🔮魅力:+47
[烟花]恩爱:+10 
🕒下次:2025-08-02 07:02:17'
2025-08-02 06:42:10 | DEBUG | 消息内容 '💕 [锦岚]👩‍❤‍👨[小爱]💕锦岚你刚刚买的兰博基尼[砖圈]带上最爱你的小爱，去旅游
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：恩爱夫妻
⛺地点：厕所
😍活动：搞一圈
[爱心]结果：依旧兼听
💓═☘︎══•💗•══☘︎═💓
🔮魅力:+47
[烟花]恩爱:+10 
🕒下次:2025-08-02 07:02:17' 不匹配任何命令，忽略
2025-08-02 06:42:11 | DEBUG | 收到消息: {'MsgId': 45492881, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8l9ymg1mafud12:\n「锦岚」[爱心]「小爱」\n地点：游艇\n活动：双修\n结果：成功\n羞羞：欲罢不能~\n恩爱值增加300\n\n下次:2025-08-02 06:52:16'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754088137, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_xgjnsizH|v1_N8dmUwef</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '不吃香菜\ue110 : 「锦岚」[爱心]「小爱」\n地点：游艇\n活动：双修\n结果：成功\n羞羞：...', 'NewMsgId': 2615663227296760463, 'MsgSeq': 871419457}
2025-08-02 06:42:11 | INFO | 收到文本消息: 消息ID:45492881 来自:48097389945@chatroom 发送人:wxid_8l9ymg1mafud12 @:[] 内容:「锦岚」[爱心]「小爱」
地点：游艇
活动：双修
结果：成功
羞羞：欲罢不能~
恩爱值增加300

下次:2025-08-02 06:52:16
2025-08-02 06:42:12 | DEBUG | [DouBaoImageToImage] 收到文本消息: '「锦岚」[爱心]「小爱」
地点：游艇
活动：双修
结果：成功
羞羞：欲罢不能~
恩爱值增加300

下次:2025-08-02 06:52:16' from wxid_8l9ymg1mafud12 in 48097389945@chatroom
2025-08-02 06:42:12 | DEBUG | [DouBaoImageToImage] 命令解析: ['「锦岚」[爱心]「小爱」\n地点：游艇\n活动：双修\n结果：成功\n羞羞：欲罢不能~\n恩爱值增加300\n\n下次:2025-08-02', '06:52:16']
2025-08-02 06:42:12 | DEBUG | 处理消息内容: '「锦岚」[爱心]「小爱」
地点：游艇
活动：双修
结果：成功
羞羞：欲罢不能~
恩爱值增加300

下次:2025-08-02 06:52:16'
2025-08-02 06:42:12 | DEBUG | 消息内容 '「锦岚」[爱心]「小爱」
地点：游艇
活动：双修
结果：成功
羞羞：欲罢不能~
恩爱值增加300

下次:2025-08-02 06:52:16' 不匹配任何命令，忽略
2025-08-02 06:46:32 | DEBUG | 收到消息: {'MsgId': 1463268571, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'last--exile:\n<msg><emoji fromusername="last--exile" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="913c8799a96ac67659912c6e00557eff" len="159498" productid="" androidmd5="913c8799a96ac67659912c6e00557eff" androidlen="159498" s60v3md5="913c8799a96ac67659912c6e00557eff" s60v3len="159498" s60v5md5="913c8799a96ac67659912c6e00557eff" s60v5len="159498" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=913c8799a96ac67659912c6e00557eff&amp;filekey=30440201010430302e02016e04025348042039313363383739396139366163363736353939313263366530303535376566660203026f0a040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032313037303430383135313830303061633639663864666463633166363736356234306230303030303036653031303034666231&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=a49a33ea265ba31642351d705e25c4c8&amp;filekey=30440201010430302e02016e04025348042061343961333365613236356261333136343233353164373035653235633463380203026f10040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032313037303430383135313830303062336563613864666463633166363736356234306230303030303036653032303034666232&amp;ef=2&amp;bizid=1022" aeskey="587c9c937fc04175af84fa88b3582253" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=2c62482504be2ed17f2e5b63008f4965&amp;filekey=3043020101042f302d02016e0402534804203263363234383235303462653265643137663265356236333030386634393635020268f0040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032313037303430383135313830303063333137383864666463633166363736356234306230303030303036653033303034666233&amp;ef=3&amp;bizid=1022" externmd5="4d360d62b49e0288e996db5907d83448" width="480" height="544" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754088401, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_1T87cVV7|v1_BG+Abqza</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一个表情', 'NewMsgId': 508262727368167582, 'MsgSeq': 871419458}
2025-08-02 06:46:32 | INFO | 收到表情消息: 消息ID:1463268571 来自:48097389945@chatroom 发送人:last--exile MD5:913c8799a96ac67659912c6e00557eff 大小:159498
2025-08-02 06:46:33 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 508262727368167582
2025-08-02 06:46:35 | DEBUG | 收到消息: {'MsgId': 544764264, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'last--exile:\n@锦岚\u2005'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754088403, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_wlnzvr8ivgd422]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_xMxE7Go4|v1_ng4zqBXO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮 : @锦岚\u2005', 'NewMsgId': 6645240834539338061, 'MsgSeq': 871419459}
2025-08-02 06:46:35 | INFO | 收到文本消息: 消息ID:544764264 来自:48097389945@chatroom 发送人:last--exile @:['wxid_wlnzvr8ivgd422'] 内容:@锦岚 
2025-08-02 06:46:36 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@锦岚' from last--exile in 48097389945@chatroom
2025-08-02 06:46:36 | DEBUG | [DouBaoImageToImage] 命令解析: ['@锦岚']
2025-08-02 06:46:36 | DEBUG | 处理消息内容: '@锦岚'
2025-08-02 06:46:36 | DEBUG | 消息内容 '@锦岚' 不匹配任何命令，忽略
2025-08-02 06:48:31 | DEBUG | 收到消息: {'MsgId': 269470099, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_zbh5p28da1si22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="2deebdc873de5e041777fb8a59fe4fc4" encryver="1" cdnthumbaeskey="2deebdc873de5e041777fb8a59fe4fc4" cdnthumburl="3057020100044b30490201000204acc2e5ef02032f58b702040c6632700204688d4424042462363966656563612d376231632d343736662d396538312d653033386430613761316163020405250a020201000405004c550700" cdnthumblength="5624" cdnthumbheight="120" cdnthumbwidth="68" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204acc2e5ef02032f58b702040c6632700204688d4424042462363966656563612d376231632d343736662d396538312d653033386430613761316163020405250a020201000405004c550700" length="173631" md5="5a840a0aa2ebdd2a788d9071eef0574d" hevc_mid_size="173631" originsourcemd5="c268566f8d4b0d62a2992bdbe02dcd9d">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjkwMzA5MDE1MTAyMTMwMjQiLCJwZHFIYXNoIjoiMTI2N2U1NzEzNjIyMDYxYWRm\nM2NiZjk3Yzk5OTYzNGU0ZWNlZWU5MDQ3MjU0OGI1YzNjZGQ5YTUxMDQxNTdlYSJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754088519, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>54639f308c0711af90c537df142f6ec0_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_keCzsEZR|v1_TkNYNHYP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2482327657209352935, 'MsgSeq': 871419460}
2025-08-02 06:48:31 | INFO | 收到图片消息: 消息ID:269470099 来自:***********@chatroom 发送人:wxid_zbh5p28da1si22 XML:<?xml version="1.0"?><msg><img aeskey="2deebdc873de5e041777fb8a59fe4fc4" encryver="1" cdnthumbaeskey="2deebdc873de5e041777fb8a59fe4fc4" cdnthumburl="3057020100044b30490201000204acc2e5ef02032f58b702040c6632700204688d4424042462363966656563612d376231632d343736662d396538312d653033386430613761316163020405250a020201000405004c550700" cdnthumblength="5624" cdnthumbheight="120" cdnthumbwidth="68" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204acc2e5ef02032f58b702040c6632700204688d4424042462363966656563612d376231632d343736662d396538312d653033386430613761316163020405250a020201000405004c550700" length="173631" md5="5a840a0aa2ebdd2a788d9071eef0574d" hevc_mid_size="173631" originsourcemd5="c268566f8d4b0d62a2992bdbe02dcd9d"><secHashInfoBase64>eyJwaGFzaCI6IjkwMzA5MDE1MTAyMTMwMjQiLCJwZHFIYXNoIjoiMTI2N2U1NzEzNjIyMDYxYWRmM2NiZjk3Yzk5OTYzNGU0ZWNlZWU5MDQ3MjU0OGI1YzNjZGQ5YTUxMDQxNTdlYSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-02 06:48:31 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-08-02 06:48:31 | INFO | [TimerTask] 缓存图片消息: 269470099
2025-08-02 06:48:39 | DEBUG | 收到消息: {'MsgId': 1970961019, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_besewpsontwy29:\n签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754088527, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_hfihGTa1|v1_/imn+vxY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5935086249826198974, 'MsgSeq': 871419461}
2025-08-02 06:48:39 | INFO | 收到文本消息: 消息ID:1970961019 来自:***********@chatroom 发送人:wxid_besewpsontwy29 @:[] 内容:签到
2025-08-02 06:48:39 | DEBUG | [DouBaoImageToImage] 收到文本消息: '签到' from wxid_besewpsontwy29 in ***********@chatroom
2025-08-02 06:48:39 | DEBUG | [DouBaoImageToImage] 命令解析: ['签到']
2025-08-02 06:48:39 | DEBUG | 处理消息内容: '签到'
2025-08-02 06:48:39 | DEBUG | 消息内容 '签到' 不匹配任何命令，忽略
2025-08-02 06:48:39 | INFO | 发送表情消息: 对方wxid:***********@chatroom md5:ae672c700aaf271a151e18a9ecf4445b 总长度:30259
2025-08-02 06:48:39 | INFO | 数据库: 用户wxid_besewpsontwy29登录时间设置为2025-08-02 00:00:00+08:00
2025-08-02 06:48:39 | INFO | 数据库: 用户wxid_besewpsontwy29连续签到天数设置为12
2025-08-02 06:48:39 | INFO | 数据库: 用户wxid_besewpsontwy29积分增加15
2025-08-02 06:48:41 | INFO | 发送文字消息: 对方wxid:***********@chatroom at:['wxid_besewpsontwy29'] 内容:@穆穆 
-----XYBot-----
签到成功！你领到了 13 个积分！✅
你是今天第 2 个签到的！🎉
你连续签到了 12 天！ 再奖励 2 积分！[爱心]
2025-08-02 06:53:43 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-02 07:04:15 | DEBUG | 收到消息: {'MsgId': 2127372730, 'FromUserName': {'string': 'weixin'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 9999, 'Content': {'string': '<newcount>3</newcount><version>900</version>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754089464, 'MsgSource': '<msgsource>\n\t<signature>v1_PjInhxhj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4654002541328034085, 'MsgSeq': 871419466}
2025-08-02 07:04:15 | INFO | 未知的消息类型: {'MsgId': 2127372730, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 9999, 'Content': {'string': '<newcount>3</newcount><version>900</version>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754089464, 'MsgSource': '<msgsource>\n\t<signature>v1_PjInhxhj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4654002541328034085, 'MsgSeq': 871419466, 'FromWxid': 'weixin'}
2025-08-02 07:04:25 | DEBUG | 收到消息: {'MsgId': 951923915, 'FromUserName': {'string': 'wxid_4usgcju5ey9q29'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 51, 'Content': {'string': '<msg>\n<op id=\'11\'>\n<name>HandOffMaster</name>\n<arg><handofflist opcode="4" seq="169" devicevirtualid="863718534fa662b1b041fc5d1d50e54f" networkstatus="4g" availablecount="1000">\n        \n        </handofflist></arg>\n</op>\n</msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754089474, 'MsgSource': '<msgsource>\n\t<signature>v1_0hMV922e</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4394427824216826041, 'MsgSeq': 871419468}
2025-08-02 07:04:33 | DEBUG | 收到消息: {'MsgId': 1868923298, 'FromUserName': {'string': 'wxid_4usgcju5ey9q29'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 51, 'Content': {'string': '<msg>\n<op id=\'11\'>\n<name>HandOffMaster</name>\n<arg><handofflist opcode="4" seq="170" devicevirtualid="863718534fa662b1b041fc5d1d50e54f" networkstatus="4g" availablecount="1000">\n        \n        </handofflist></arg>\n</op>\n</msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754089482, 'MsgSource': '<msgsource>\n\t<signature>v1_KGIQbYjt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 706507597531262721, 'MsgSeq': 871419469}
2025-08-02 07:05:04 | DEBUG | 收到消息: {'MsgId': 974653217, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n卧槽，等维护等睡着了，各种没领'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754089512, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_/hIh8uJ4|v1_e1dEOAdh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 558866198870197870, 'MsgSeq': 871419470}
2025-08-02 07:05:04 | INFO | 收到文本消息: 消息ID:974653217 来自:***********@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:卧槽，等维护等睡着了，各种没领
2025-08-02 07:05:04 | DEBUG | [DouBaoImageToImage] 收到文本消息: '卧槽，等维护等睡着了，各种没领' from wxid_ubbh6q832tcs21 in ***********@chatroom
2025-08-02 07:05:04 | DEBUG | [DouBaoImageToImage] 命令解析: ['卧槽，等维护等睡着了，各种没领']
2025-08-02 07:05:04 | DEBUG | 处理消息内容: '卧槽，等维护等睡着了，各种没领'
2025-08-02 07:05:04 | DEBUG | 消息内容 '卧槽，等维护等睡着了，各种没领' 不匹配任何命令，忽略
2025-08-02 07:05:20 | DEBUG | 收到消息: {'MsgId': 1962849753, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="9fe3ebbae6db05d1ffc7ce333f276e84" len = "500863" productid="" androidmd5="9fe3ebbae6db05d1ffc7ce333f276e84" androidlen="500863" s60v3md5 = "9fe3ebbae6db05d1ffc7ce333f276e84" s60v3len="500863" s60v5md5 = "9fe3ebbae6db05d1ffc7ce333f276e84" s60v5len="500863" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=9fe3ebbae6db05d1ffc7ce333f276e84&amp;filekey=30440201010430302e02016e0402535a04203966653365626261653664623035643166666337636533333366323736653834020307a47f040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26321b145000ceb80a95c809d0000006e01004fb1535a189c4b50b64b5ba12&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=d2751efb7fb6c357a499078c00b8afb4&amp;filekey=30440201010430302e02016e0402535a04206432373531656662376662366333353761343939303738633030623861666234020307a480040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26321b145000e5b07a95c809d0000006e02004fb2535a189c4b50b64b5ba3d&amp;ef=2&amp;bizid=1022" aeskey= "00fc360e0f924a969e8bf5cccf9d859e" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=f7b6df2e3fafdaf8fd7b07859e9c4f63&amp;filekey=30440201010430302e02016e0402535a04206637623664663265336661666461663866643762303738353965396334663633020300ace0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26321b14600009627a95c809d0000006e03004fb3535a189c4b50b64b5ba5a&amp;ef=3&amp;bizid=1022" externmd5 = "2b1401d747b8cf84e741cd8b15a19b00" width= "400" height= "400" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754089529, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_Bv/SRzLU|v1_WBPXbAVe</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3268880346735197546, 'MsgSeq': 871419471}
2025-08-02 07:05:20 | INFO | 收到表情消息: 消息ID:1962849753 来自:***********@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:9fe3ebbae6db05d1ffc7ce333f276e84 大小:500863
2025-08-02 07:05:20 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3268880346735197546
2025-08-02 07:07:52 | DEBUG | 收到消息: {'MsgId': 567980445, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n几点维护的？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754089680, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_B29TxHQC|v1_sJNXk2ah</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4097686184791838483, 'MsgSeq': 871419472}
2025-08-02 07:07:52 | INFO | 收到文本消息: 消息ID:567980445 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:几点维护的？
2025-08-02 07:07:52 | DEBUG | [DouBaoImageToImage] 收到文本消息: '几点维护的？' from wxid_snv13qf05qjx11 in ***********@chatroom
2025-08-02 07:07:52 | DEBUG | [DouBaoImageToImage] 命令解析: ['几点维护的？']
2025-08-02 07:07:52 | DEBUG | 处理消息内容: '几点维护的？'
2025-08-02 07:07:52 | DEBUG | 消息内容 '几点维护的？' 不匹配任何命令，忽略
2025-08-02 07:13:58 | DEBUG | 收到消息: {'MsgId': 1665753117, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n就晚上9点55那会吧，然后就睡着了[发呆]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754090046, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_ZWFVsmSB|v1_0mJ9HQ+T</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5336583164655738007, 'MsgSeq': 871419473}
2025-08-02 07:13:58 | INFO | 收到文本消息: 消息ID:1665753117 来自:***********@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:就晚上9点55那会吧，然后就睡着了[发呆]
2025-08-02 07:13:58 | DEBUG | [DouBaoImageToImage] 收到文本消息: '就晚上9点55那会吧，然后就睡着了[发呆]' from wxid_ubbh6q832tcs21 in ***********@chatroom
2025-08-02 07:13:58 | DEBUG | [DouBaoImageToImage] 命令解析: ['就晚上9点55那会吧，然后就睡着了[发呆]']
2025-08-02 07:13:58 | DEBUG | 处理消息内容: '就晚上9点55那会吧，然后就睡着了[发呆]'
2025-08-02 07:13:58 | DEBUG | 消息内容 '就晚上9点55那会吧，然后就睡着了[发呆]' 不匹配任何命令，忽略
2025-08-02 07:17:18 | DEBUG | 收到消息: {'MsgId': 822025725, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_yxdvl6zp4er522:\n大半夜'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754090246, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_oWZ2CR+W|v1_XVbRibYy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5859218586153385717, 'MsgSeq': 871419474}
2025-08-02 07:17:18 | INFO | 收到文本消息: 消息ID:822025725 来自:***********@chatroom 发送人:wxid_yxdvl6zp4er522 @:[] 内容:大半夜
2025-08-02 07:17:18 | DEBUG | [DouBaoImageToImage] 收到文本消息: '大半夜' from wxid_yxdvl6zp4er522 in ***********@chatroom
2025-08-02 07:17:18 | DEBUG | [DouBaoImageToImage] 命令解析: ['大半夜']
2025-08-02 07:17:18 | DEBUG | 处理消息内容: '大半夜'
2025-08-02 07:17:18 | DEBUG | 消息内容 '大半夜' 不匹配任何命令，忽略
2025-08-02 07:18:02 | DEBUG | 收到消息: {'MsgId': 1884245175, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n我刚醒来 看我号都卡住了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754090291, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_spJmyQGV|v1_PCf2w5Pd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4885673066238002058, 'MsgSeq': 871419475}
2025-08-02 07:18:02 | INFO | 收到文本消息: 消息ID:1884245175 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:我刚醒来 看我号都卡住了
2025-08-02 07:18:02 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我刚醒来 看我号都卡住了' from wxid_snv13qf05qjx11 in ***********@chatroom
2025-08-02 07:18:02 | DEBUG | [DouBaoImageToImage] 命令解析: ['我刚醒来', '看我号都卡住了']
2025-08-02 07:18:02 | DEBUG | 处理消息内容: '我刚醒来 看我号都卡住了'
2025-08-02 07:18:02 | DEBUG | 消息内容 '我刚醒来 看我号都卡住了' 不匹配任何命令，忽略
2025-08-02 07:18:22 | DEBUG | 收到消息: {'MsgId': 1617391463, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_yxdvl6zp4er522:\n嗯，估摸着三四点的时候'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754090310, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_uxJqNBXM|v1_j3ZsCi0E</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6559816891112384555, 'MsgSeq': 871419476}
2025-08-02 07:18:22 | INFO | 收到文本消息: 消息ID:1617391463 来自:***********@chatroom 发送人:wxid_yxdvl6zp4er522 @:[] 内容:嗯，估摸着三四点的时候
2025-08-02 07:18:22 | DEBUG | [DouBaoImageToImage] 收到文本消息: '嗯，估摸着三四点的时候' from wxid_yxdvl6zp4er522 in ***********@chatroom
2025-08-02 07:18:22 | DEBUG | [DouBaoImageToImage] 命令解析: ['嗯，估摸着三四点的时候']
2025-08-02 07:18:22 | DEBUG | 处理消息内容: '嗯，估摸着三四点的时候'
2025-08-02 07:18:22 | DEBUG | 消息内容 '嗯，估摸着三四点的时候' 不匹配任何命令，忽略
2025-08-02 07:18:35 | DEBUG | 收到消息: {'MsgId': 1358765011, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_yxdvl6zp4er522:\n毕竟两点还玩着呢'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754090323, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_d9Dy8+yu|v1_0lSPULrX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1675244089575938226, 'MsgSeq': 871419477}
2025-08-02 07:18:35 | INFO | 收到文本消息: 消息ID:1358765011 来自:***********@chatroom 发送人:wxid_yxdvl6zp4er522 @:[] 内容:毕竟两点还玩着呢
2025-08-02 07:18:35 | DEBUG | [DouBaoImageToImage] 收到文本消息: '毕竟两点还玩着呢' from wxid_yxdvl6zp4er522 in ***********@chatroom
2025-08-02 07:18:35 | DEBUG | [DouBaoImageToImage] 命令解析: ['毕竟两点还玩着呢']
2025-08-02 07:18:35 | DEBUG | 处理消息内容: '毕竟两点还玩着呢'
2025-08-02 07:18:35 | DEBUG | 消息内容 '毕竟两点还玩着呢' 不匹配任何命令，忽略
2025-08-02 07:18:42 | DEBUG | 收到消息: {'MsgId': 1466150220, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n真有病'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754090331, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_Kc8pGFtS|v1_wzHT+He1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4875082852101484562, 'MsgSeq': 871419478}
2025-08-02 07:18:42 | INFO | 收到文本消息: 消息ID:1466150220 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:真有病
2025-08-02 07:18:42 | DEBUG | [DouBaoImageToImage] 收到文本消息: '真有病' from wxid_snv13qf05qjx11 in ***********@chatroom
2025-08-02 07:18:42 | DEBUG | [DouBaoImageToImage] 命令解析: ['真有病']
2025-08-02 07:18:42 | DEBUG | 处理消息内容: '真有病'
2025-08-02 07:18:42 | DEBUG | 消息内容 '真有病' 不匹配任何命令，忽略
2025-08-02 07:19:08 | DEBUG | 收到消息: {'MsgId': 588025727, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n我看你挂着房呢'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754090356, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_M0KdAX37|v1_hlI9J1i8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7483832608269815700, 'MsgSeq': 871419479}
2025-08-02 07:19:08 | INFO | 收到文本消息: 消息ID:588025727 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:我看你挂着房呢
2025-08-02 07:19:08 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我看你挂着房呢' from wxid_snv13qf05qjx11 in ***********@chatroom
2025-08-02 07:19:08 | DEBUG | [DouBaoImageToImage] 命令解析: ['我看你挂着房呢']
2025-08-02 07:19:08 | DEBUG | 处理消息内容: '我看你挂着房呢'
2025-08-02 07:19:08 | DEBUG | 消息内容 '我看你挂着房呢' 不匹配任何命令，忽略
2025-08-02 07:19:24 | DEBUG | 收到消息: {'MsgId': 1902380053, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_yxdvl6zp4er522:\n我上去就俩房间'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754090372, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_YyqAeciH|v1_3VM1gY6i</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4672252998258798483, 'MsgSeq': 871419480}
2025-08-02 07:19:24 | INFO | 收到文本消息: 消息ID:1902380053 来自:***********@chatroom 发送人:wxid_yxdvl6zp4er522 @:[] 内容:我上去就俩房间
2025-08-02 07:19:24 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我上去就俩房间' from wxid_yxdvl6zp4er522 in ***********@chatroom
2025-08-02 07:19:24 | DEBUG | [DouBaoImageToImage] 命令解析: ['我上去就俩房间']
2025-08-02 07:19:24 | DEBUG | 处理消息内容: '我上去就俩房间'
2025-08-02 07:19:24 | DEBUG | 消息内容 '我上去就俩房间' 不匹配任何命令，忽略
2025-08-02 07:19:32 | DEBUG | 收到消息: {'MsgId': 699084702, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n李诚7.30醒 你在坚持一下'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754090381, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_K1IGO1HG|v1_mdXEvc5U</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5807623403004222321, 'MsgSeq': 871419481}
2025-08-02 07:19:32 | INFO | 收到文本消息: 消息ID:699084702 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:李诚7.30醒 你在坚持一下
2025-08-02 07:19:32 | DEBUG | [DouBaoImageToImage] 收到文本消息: '李诚7.30醒 你在坚持一下' from wxid_snv13qf05qjx11 in ***********@chatroom
2025-08-02 07:19:32 | DEBUG | [DouBaoImageToImage] 命令解析: ['李诚7.30醒', '你在坚持一下']
2025-08-02 07:19:32 | DEBUG | 处理消息内容: '李诚7.30醒 你在坚持一下'
2025-08-02 07:19:32 | DEBUG | 消息内容 '李诚7.30醒 你在坚持一下' 不匹配任何命令，忽略
2025-08-02 07:19:34 | DEBUG | 收到消息: {'MsgId': 374783169, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_yxdvl6zp4er522:\n我还以为我眼花了[捂脸]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754090382, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_jR+FzUey|v1_ecwZz/pS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6018502522202024606, 'MsgSeq': 871419482}
2025-08-02 07:19:34 | INFO | 收到文本消息: 消息ID:374783169 来自:***********@chatroom 发送人:wxid_yxdvl6zp4er522 @:[] 内容:我还以为我眼花了[捂脸]
2025-08-02 07:19:34 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我还以为我眼花了[捂脸]' from wxid_yxdvl6zp4er522 in ***********@chatroom
2025-08-02 07:19:34 | DEBUG | [DouBaoImageToImage] 命令解析: ['我还以为我眼花了[捂脸]']
2025-08-02 07:19:34 | DEBUG | 处理消息内容: '我还以为我眼花了[捂脸]'
2025-08-02 07:19:34 | DEBUG | 消息内容 '我还以为我眼花了[捂脸]' 不匹配任何命令，忽略
2025-08-02 07:19:46 | DEBUG | 收到消息: {'MsgId': 1097675776, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_yxdvl6zp4er522:\n没事，后台挂着呢'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754090395, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_VJQ43Fly|v1_AztqXgOL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3594951028306071515, 'MsgSeq': 871419483}
2025-08-02 07:19:46 | INFO | 收到文本消息: 消息ID:1097675776 来自:***********@chatroom 发送人:wxid_yxdvl6zp4er522 @:[] 内容:没事，后台挂着呢
2025-08-02 07:19:46 | DEBUG | [DouBaoImageToImage] 收到文本消息: '没事，后台挂着呢' from wxid_yxdvl6zp4er522 in ***********@chatroom
2025-08-02 07:19:46 | DEBUG | [DouBaoImageToImage] 命令解析: ['没事，后台挂着呢']
2025-08-02 07:19:46 | DEBUG | 处理消息内容: '没事，后台挂着呢'
2025-08-02 07:19:46 | DEBUG | 消息内容 '没事，后台挂着呢' 不匹配任何命令，忽略
2025-08-02 07:20:38 | DEBUG | 收到消息: {'MsgId': 1708010466, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n我以为我手机卡了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754090447, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_LN/4DHP1|v1_Gfg2h3La</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8305737232804841673, 'MsgSeq': 871419484}
2025-08-02 07:20:38 | INFO | 收到文本消息: 消息ID:1708010466 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:我以为我手机卡了
2025-08-02 07:20:38 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我以为我手机卡了' from wxid_snv13qf05qjx11 in ***********@chatroom
2025-08-02 07:20:38 | DEBUG | [DouBaoImageToImage] 命令解析: ['我以为我手机卡了']
2025-08-02 07:20:38 | DEBUG | 处理消息内容: '我以为我手机卡了'
2025-08-02 07:20:38 | DEBUG | 消息内容 '我以为我手机卡了' 不匹配任何命令，忽略
2025-08-02 07:20:58 | DEBUG | 收到消息: {'MsgId': 1223578581, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n还切换了一下网 笑死'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754090467, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_ZTYxBtIL|v1_t8K4Sq7X</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 983307845574605238, 'MsgSeq': 871419485}
2025-08-02 07:20:58 | INFO | 收到文本消息: 消息ID:1223578581 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:还切换了一下网 笑死
2025-08-02 07:20:58 | DEBUG | [DouBaoImageToImage] 收到文本消息: '还切换了一下网 笑死' from wxid_snv13qf05qjx11 in ***********@chatroom
2025-08-02 07:20:58 | DEBUG | [DouBaoImageToImage] 命令解析: ['还切换了一下网', '笑死']
2025-08-02 07:20:58 | DEBUG | 处理消息内容: '还切换了一下网 笑死'
2025-08-02 07:20:58 | DEBUG | 消息内容 '还切换了一下网 笑死' 不匹配任何命令，忽略
2025-08-02 07:22:13 | DEBUG | 收到消息: {'MsgId': 978689639, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_yxdvl6zp4er522:\n我一上后台，看卡在跳舞最后，还以为只有我俩卡了，重上一看，都好几个小时前[捂脸]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754090542, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_xC8Hp6M2|v1_YtUzNAZ2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8936957471510427120, 'MsgSeq': 871419486}
2025-08-02 07:22:13 | INFO | 收到文本消息: 消息ID:978689639 来自:***********@chatroom 发送人:wxid_yxdvl6zp4er522 @:[] 内容:我一上后台，看卡在跳舞最后，还以为只有我俩卡了，重上一看，都好几个小时前[捂脸]
2025-08-02 07:22:13 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我一上后台，看卡在跳舞最后，还以为只有我俩卡了，重上一看，都好几个小时前[捂脸]' from wxid_yxdvl6zp4er522 in ***********@chatroom
2025-08-02 07:22:13 | DEBUG | [DouBaoImageToImage] 命令解析: ['我一上后台，看卡在跳舞最后，还以为只有我俩卡了，重上一看，都好几个小时前[捂脸]']
2025-08-02 07:22:13 | DEBUG | 处理消息内容: '我一上后台，看卡在跳舞最后，还以为只有我俩卡了，重上一看，都好几个小时前[捂脸]'
2025-08-02 07:22:13 | DEBUG | 消息内容 '我一上后台，看卡在跳舞最后，还以为只有我俩卡了，重上一看，都好几个小时前[捂脸]' 不匹配任何命令，忽略
2025-08-02 07:23:43 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-02 07:23:43 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-08-02 07:23:44 | DEBUG | 群成员变化检查完成
2025-08-02 07:25:38 | DEBUG | 收到消息: {'MsgId': 268454648, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n我也'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754090747, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_DCUfsW6M|v1_9Y/TF6vK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 303844736210115664, 'MsgSeq': 871419487}
2025-08-02 07:25:38 | INFO | 收到文本消息: 消息ID:268454648 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:我也
2025-08-02 07:25:38 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我也' from wxid_snv13qf05qjx11 in ***********@chatroom
2025-08-02 07:25:38 | DEBUG | [DouBaoImageToImage] 命令解析: ['我也']
2025-08-02 07:25:38 | DEBUG | 处理消息内容: '我也'
2025-08-02 07:25:38 | DEBUG | 消息内容 '我也' 不匹配任何命令，忽略
2025-08-02 07:26:04 | DEBUG | 收到消息: {'MsgId': 1803811105, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n这游戏是真做不明白'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754090773, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_4Qn7GmJi|v1_RAOQWtvi</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1218970836931422639, 'MsgSeq': 871419488}
2025-08-02 07:26:04 | INFO | 收到文本消息: 消息ID:1803811105 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:这游戏是真做不明白
2025-08-02 07:26:04 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这游戏是真做不明白' from wxid_snv13qf05qjx11 in ***********@chatroom
2025-08-02 07:26:04 | DEBUG | [DouBaoImageToImage] 命令解析: ['这游戏是真做不明白']
2025-08-02 07:26:04 | DEBUG | 处理消息内容: '这游戏是真做不明白'
2025-08-02 07:26:04 | DEBUG | 消息内容 '这游戏是真做不明白' 不匹配任何命令，忽略
2025-08-02 07:53:43 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-02 07:54:11 | DEBUG | 收到消息: {'MsgId': 1788045221, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_ctp9qffuf14b21:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="d90764b2fde6270275a59c1116c53e07" encryver="1" cdnthumbaeskey="d90764b2fde6270275a59c1116c53e07" cdnthumburl="3057020100044b304902010002040ec6fb3402032f53a10204a28f8cb60204688d5313042435643630613131392d666263342d343830372d613733342d353366313137386666646562020405150a020201000405004c50b900" cdnthumblength="4469" cdnthumbheight="197" cdnthumbwidth="433" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002040ec6fb3402032f53a10204a28f8cb60204688d5313042435643630613131392d666263342d343830372d613733342d353366313137386666646562020405150a020201000405004c50b900" length="1030453" md5="1583e669d345a5eb9f1407b39cccbe43" hevc_mid_size="101936" originsourcemd5="7e61201bffffc4fd26353054cef13c43">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6ImY1NTAwMDEwMjIwMDEwMDAiLCJwZHFIYXNoIjoiNTViZTkxYjZiZGRhOTRiMTQ3\nNGJhZDIzYjU4Mzk4NjExNWE5NGIxOGMzYjdjYzJlODRkNmZmMDRjYTM0YzZkOCJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754092459, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>9fcb49148a078ebb5eb81ebf70738dbb_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_sJtlnRxJ|v1_OAyrTv1G</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2599908642710002029, 'MsgSeq': 871419489}
2025-08-02 07:54:11 | INFO | 收到图片消息: 消息ID:1788045221 来自:***********@chatroom 发送人:wxid_ctp9qffuf14b21 XML:<?xml version="1.0"?><msg><img aeskey="d90764b2fde6270275a59c1116c53e07" encryver="1" cdnthumbaeskey="d90764b2fde6270275a59c1116c53e07" cdnthumburl="3057020100044b304902010002040ec6fb3402032f53a10204a28f8cb60204688d5313042435643630613131392d666263342d343830372d613733342d353366313137386666646562020405150a020201000405004c50b900" cdnthumblength="4469" cdnthumbheight="197" cdnthumbwidth="433" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002040ec6fb3402032f53a10204a28f8cb60204688d5313042435643630613131392d666263342d343830372d613733342d353366313137386666646562020405150a020201000405004c50b900" length="1030453" md5="1583e669d345a5eb9f1407b39cccbe43" hevc_mid_size="101936" originsourcemd5="7e61201bffffc4fd26353054cef13c43"><secHashInfoBase64>eyJwaGFzaCI6ImY1NTAwMDEwMjIwMDEwMDAiLCJwZHFIYXNoIjoiNTViZTkxYjZiZGRhOTRiMTQ3NGJhZDIzYjU4Mzk4NjExNWE5NGIxOGMzYjdjYzJlODRkNmZmMDRjYTM0YzZkOCJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-02 07:54:11 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-08-02 07:54:11 | INFO | [TimerTask] 缓存图片消息: 1788045221
2025-08-02 07:54:22 | DEBUG | 收到消息: {'MsgId': 130752578, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ctp9qffuf14b21:\n我在房间还能跑，笑死'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754092470, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_RH1C9x39|v1_hYdNtZhk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5911019548691701969, 'MsgSeq': 871419490}
2025-08-02 07:54:22 | INFO | 收到文本消息: 消息ID:130752578 来自:***********@chatroom 发送人:wxid_ctp9qffuf14b21 @:[] 内容:我在房间还能跑，笑死
2025-08-02 07:54:22 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我在房间还能跑，笑死' from wxid_ctp9qffuf14b21 in ***********@chatroom
2025-08-02 07:54:22 | DEBUG | [DouBaoImageToImage] 命令解析: ['我在房间还能跑，笑死']
2025-08-02 07:54:22 | DEBUG | 处理消息内容: '我在房间还能跑，笑死'
2025-08-02 07:54:22 | DEBUG | 消息内容 '我在房间还能跑，笑死' 不匹配任何命令，忽略
2025-08-02 07:54:42 | DEBUG | 收到消息: {'MsgId': 1555282163, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ctp9qffuf14b21:\n六点多还要蹦[捂脸]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754092490, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_guEKIXM1|v1_ie2NNaPc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8061073110743898973, 'MsgSeq': 871419491}
2025-08-02 07:54:42 | INFO | 收到文本消息: 消息ID:1555282163 来自:***********@chatroom 发送人:wxid_ctp9qffuf14b21 @:[] 内容:六点多还要蹦[捂脸]
2025-08-02 07:54:42 | DEBUG | [DouBaoImageToImage] 收到文本消息: '六点多还要蹦[捂脸]' from wxid_ctp9qffuf14b21 in ***********@chatroom
2025-08-02 07:54:42 | DEBUG | [DouBaoImageToImage] 命令解析: ['六点多还要蹦[捂脸]']
2025-08-02 07:54:42 | DEBUG | 处理消息内容: '六点多还要蹦[捂脸]'
2025-08-02 07:54:42 | DEBUG | 消息内容 '六点多还要蹦[捂脸]' 不匹配任何命令，忽略
2025-08-02 07:54:55 | DEBUG | 收到消息: {'MsgId': 1243971127, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': 'wxid_ctp9qffuf14b21:\n<sysmsg type="revokemsg"><revokemsg><session>***********@chatroom</session><msgid>2134448045</msgid><newmsgid>8061073110743898973</newmsgid><replacemsg><![CDATA["Z⁰" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754092496, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8751888812601858301, 'MsgSeq': 871419492}
2025-08-02 07:54:55 | DEBUG | 系统消息类型: revokemsg
2025-08-02 07:54:55 | INFO | 未知的系统消息类型: {'MsgId': 1243971127, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="revokemsg"><revokemsg><session>***********@chatroom</session><msgid>2134448045</msgid><newmsgid>8061073110743898973</newmsgid><replacemsg><![CDATA["Z⁰" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754092496, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8751888812601858301, 'MsgSeq': 871419492, 'FromWxid': '***********@chatroom', 'IsGroup': True, 'SenderWxid': 'wxid_ctp9qffuf14b21'}
2025-08-02 07:55:53 | DEBUG | 收到消息: {'MsgId': 53667996, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': 'wxid_ctp9qffuf14b21:\n<sysmsg type="revokemsg"><revokemsg><session>***********@chatroom</session><msgid>29195143</msgid><newmsgid>2599908642710002029</newmsgid><replacemsg><![CDATA["Z⁰" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754092558, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9027701942579190311, 'MsgSeq': 871419493}
2025-08-02 07:55:53 | DEBUG | 系统消息类型: revokemsg
2025-08-02 07:55:53 | INFO | 未知的系统消息类型: {'MsgId': 53667996, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="revokemsg"><revokemsg><session>***********@chatroom</session><msgid>29195143</msgid><newmsgid>2599908642710002029</newmsgid><replacemsg><![CDATA["Z⁰" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754092558, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9027701942579190311, 'MsgSeq': 871419493, 'FromWxid': '***********@chatroom', 'IsGroup': True, 'SenderWxid': 'wxid_ctp9qffuf14b21'}
2025-08-02 07:55:55 | DEBUG | 收到消息: {'MsgId': 1951471107, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': 'wxid_ctp9qffuf14b21:\n<sysmsg type="revokemsg"><revokemsg><session>***********@chatroom</session><msgid>56868305</msgid><newmsgid>5911019548691701969</newmsgid><replacemsg><![CDATA["Z⁰" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754092560, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5419992587303725251, 'MsgSeq': 871419494}
2025-08-02 07:55:55 | DEBUG | 系统消息类型: revokemsg
2025-08-02 07:55:55 | INFO | 未知的系统消息类型: {'MsgId': 1951471107, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="revokemsg"><revokemsg><session>***********@chatroom</session><msgid>56868305</msgid><newmsgid>5911019548691701969</newmsgid><replacemsg><![CDATA["Z⁰" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754092560, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5419992587303725251, 'MsgSeq': 871419494, 'FromWxid': '***********@chatroom', 'IsGroup': True, 'SenderWxid': 'wxid_ctp9qffuf14b21'}
2025-08-02 07:56:18 | DEBUG | 收到消息: {'MsgId': 1727578910, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754092587, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[]]></atuserlist>\n\t<silence>1</silence>\n\t<membercount>148</membercount>\n\t<signature>N0_V1_v7lmaO67|v1_p/sBkUV0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 272072189331334755, 'MsgSeq': 871419495}
2025-08-02 07:56:18 | INFO | 收到文本消息: 消息ID:1727578910 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:？
2025-08-02 07:56:18 | DEBUG | [DouBaoImageToImage] 收到文本消息: '？' from wxid_ikxxrwasicud11 in ***********@chatroom
2025-08-02 07:56:18 | DEBUG | [DouBaoImageToImage] 命令解析: ['？']
2025-08-02 07:56:18 | DEBUG | 处理消息内容: '？'
2025-08-02 07:56:18 | DEBUG | 消息内容 '？' 不匹配任何命令，忽略
2025-08-02 08:00:00 | INFO | [QuarkSignIn] 开始执行定时自动签到任务
2025-08-02 08:00:00 | DEBUG | [QuarkSignIn] API响应状态码: 200
2025-08-02 08:00:01 | DEBUG | [QuarkSignIn] 签到响应状态码: 200
2025-08-02 08:00:01 | INFO | 发送文字消息: 对方wxid:wxid_ubbh6q832tcs21 at: 内容:🤖 夸克网盘自动签到完成

🌟 夸克网盘签到开始
📊 检测到 1 个账号

🔄 第1个账号签到中...
👤 普通用户 夸父4527
💾 网盘总容量：19.98 GB
📈 签到累计容量：5.98 GB
🎉 今日签到成功+20.00 MB，连签进度(3/7)

✨ 夸克网盘签到完成
2025-08-02 08:00:01 | INFO | [QuarkSignIn] 已发送签到通知到: wxid_ubbh6q832tcs21
2025-08-02 08:03:38 | DEBUG | 收到消息: {'MsgId': 1479001367, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n早早早'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754093026, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_+7A09+/Q|v1_p+zPByBX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 早早早', 'NewMsgId': 3353504483042494724, 'MsgSeq': 871419498}
2025-08-02 08:03:38 | INFO | 收到文本消息: 消息ID:1479001367 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:早早早
2025-08-02 08:03:38 | DEBUG | [DouBaoImageToImage] 收到文本消息: '早早早' from wxid_wlnzvr8ivgd422 in 48097389945@chatroom
2025-08-02 08:03:38 | DEBUG | [DouBaoImageToImage] 命令解析: ['早早早']
2025-08-02 08:03:38 | DEBUG | 处理消息内容: '早早早'
2025-08-02 08:03:38 | DEBUG | 消息内容 '早早早' 不匹配任何命令，忽略
2025-08-02 08:05:12 | DEBUG | 收到消息: {'MsgId': 1422407960, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'last--exile:\n<msg><emoji fromusername="last--exile" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="f2483df403265d9e166c808d2e4dd392" len="20561" productid="" androidmd5="f2483df403265d9e166c808d2e4dd392" androidlen="20561" s60v3md5="f2483df403265d9e166c808d2e4dd392" s60v3len="20561" s60v5md5="f2483df403265d9e166c808d2e4dd392" s60v5len="20561" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=f2483df403265d9e166c808d2e4dd392&amp;filekey=30340201010420301e02020106040253480410f2483df403265d9e166c808d2e4dd39202025051040d00000004627466730000000132&amp;hy=SH&amp;storeid=26322fd9e00070f78000000000000010600004f5053482edc596097bc05b5e&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=0493fbf5b15f3dcc73508cbad355587c&amp;filekey=30340201010420301e020201060402534804100493fbf5b15f3dcc73508cbad355587c02025060040d00000004627466730000000132&amp;hy=SH&amp;storeid=26322fd9e0009eea1000000000000010600004f5053481166fb40b7c65f645&amp;bizid=1023" aeskey="df8c4443cabc6e6dfc6985a591b83398" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=26abde792de8f7399e2a6237027bca0d&amp;filekey=30340201010420301e0202010604025348041026abde792de8f7399e2a6237027bca0d020220b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26322fd9e000beda8000000000000010600004f5053480ec67b40b7ca534f2&amp;bizid=1023" externmd5="e4cf1d632fff0d631859feeae7612d68" width="255" height="255" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754093120, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_1wTtPfBr|v1_r3txjdej</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一个表情', 'NewMsgId': 3922480321320472959, 'MsgSeq': 871419499}
2025-08-02 08:05:12 | INFO | 收到表情消息: 消息ID:1422407960 来自:48097389945@chatroom 发送人:last--exile MD5:f2483df403265d9e166c808d2e4dd392 大小:20561
2025-08-02 08:05:12 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3922480321320472959
2025-08-02 08:11:59 | DEBUG | 收到消息: {'MsgId': 1788305760, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="6d63414e66085304271b5922591d53e6" encryver="1" cdnthumbaeskey="6d63414e66085304271b5922591d53e6" cdnthumburl="3057020100044b304902010002046b3e4bb802032f77f50204685b90db0204688d57d7042435643962636131312d373661622d346663632d383730362d663334643335366635633830020405290a020201000405004c4d3500" cdnthumblength="5969" cdnthumbheight="144" cdnthumbwidth="57" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f77f50204685b90db0204688d57d7042435643962636131312d373661622d346663632d383730362d663334643335366635633830020405290a020201000405004c4d3500" length="221948" md5="23e81957e7884771516673f0e649b1b5" originsourcemd5="d2d3c71f75e7b02c8518cf75662df335">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754093527, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>b6b2086ad01e6158d21e1736894a9294_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_i6gFA/ET|v1_8aCUC9X/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一张图片', 'NewMsgId': 1937085083172748351, 'MsgSeq': 871419500}
2025-08-02 08:11:59 | INFO | 收到图片消息: 消息ID:1788305760 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 XML:<?xml version="1.0"?><msg><img aeskey="6d63414e66085304271b5922591d53e6" encryver="1" cdnthumbaeskey="6d63414e66085304271b5922591d53e6" cdnthumburl="3057020100044b304902010002046b3e4bb802032f77f50204685b90db0204688d57d7042435643962636131312d373661622d346663632d383730362d663334643335366635633830020405290a020201000405004c4d3500" cdnthumblength="5969" cdnthumbheight="144" cdnthumbwidth="57" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f77f50204685b90db0204688d57d7042435643962636131312d373661622d346663632d383730362d663334643335366635633830020405290a020201000405004c4d3500" length="221948" md5="23e81957e7884771516673f0e649b1b5" originsourcemd5="d2d3c71f75e7b02c8518cf75662df335"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-02 08:12:00 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-08-02 08:12:00 | INFO | [TimerTask] 缓存图片消息: 1788305760
2025-08-02 08:12:01 | DEBUG | 收到消息: {'MsgId': 1443635808, 'FromUserName': {'string': '43607022446@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="4961960c7776315952640c1a2b049c6e" encryver="1" cdnthumbaeskey="4961960c7776315952640c1a2b049c6e" cdnthumburl="3057020100044b304902010002046b3e4bb802032f51490204d63122750204688d57d9042463633931616335662d636431612d343665332d383462312d633931613864656230623334020405250a020201000405004c51e500" cdnthumblength="5969" cdnthumbheight="144" cdnthumbwidth="57" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f51490204d63122750204688d57d9042463633931616335662d636431612d343665332d383462312d633931613864656230623334020405250a020201000405004c51e500" length="221948" md5="23e81957e7884771516673f0e649b1b5" originsourcemd5="d2d3c71f75e7b02c8518cf75662df335">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754093529, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>65614152bbb44ff933a5d921da40a720_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>76</membercount>\n\t<signature>N0_V1_ahKJzlQh|v1_+7M+nr+e</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '吃瓜小爱\ue124\ue124\u2005在群聊中发了一张图片', 'NewMsgId': 5059668252762622083, 'MsgSeq': 871419501}
2025-08-02 08:12:01 | INFO | 收到图片消息: 消息ID:1443635808 来自:43607022446@chatroom 发送人:wxid_lneb7n23o4lg12 XML:<?xml version="1.0"?><msg><img aeskey="4961960c7776315952640c1a2b049c6e" encryver="1" cdnthumbaeskey="4961960c7776315952640c1a2b049c6e" cdnthumburl="3057020100044b304902010002046b3e4bb802032f51490204d63122750204688d57d9042463633931616335662d636431612d343665332d383462312d633931613864656230623334020405250a020201000405004c51e500" cdnthumblength="5969" cdnthumbheight="144" cdnthumbwidth="57" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f51490204d63122750204688d57d9042463633931616335662d636431612d343665332d383462312d633931613864656230623334020405250a020201000405004c51e500" length="221948" md5="23e81957e7884771516673f0e649b1b5" originsourcemd5="d2d3c71f75e7b02c8518cf75662df335"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-02 08:12:01 | INFO | [ImageEcho] 保存图片信息成功，当前群 43607022446@chatroom 已存储 5 张图片
2025-08-02 08:12:01 | INFO | [TimerTask] 缓存图片消息: 1443635808
